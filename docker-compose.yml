# This is a skeleton docker-compose.yml file
# The candidate should complete this for the bonus task

version: '3.8'

services:
  # MongoDB service
  mongodb:
    image: mongo:latest
    # TODO: Configure MongoDB container
    
  # Backend service
  backend:
    build:
      context: ./backend
    # TODO: Configure backend container and dependencies
    
  # Frontend service
  frontend:
    build:
      context: ./frontend
    # TODO: Configure frontend container and dependencies
    
# TODO: Add volumes and networks configuration
