'use client';

import React, { useState } from 'react';
import { Goal } from '@/types/goal';
import { AllocationConstraints } from '@/types/goal';

interface AllocationFormProps {
  goals: Goal[];
  onSubmit: (
    amount: number,
    strategy: 'priority' | 'timeline' | 'balanced',
    constraints?: AllocationConstraints,
    source?: string
  ) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
}

const AllocationForm: React.FC<AllocationFormProps> = ({
  goals,
  onSubmit,
  onCancel,
  isLoading = false
}) => {
  const [amount, setAmount] = useState<number>(0);
  const [strategy, setStrategy] = useState<'priority' | 'timeline' | 'balanced'>('priority');
  const [source, setSource] = useState<string>('');
  const [constraints, setConstraints] = useState<AllocationConstraints>({});
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!amount || amount <= 0) {
      newErrors.amount = 'Amount must be greater than 0';
    }

    if (constraints.minimumAllocation && constraints.minimumAllocation < 0) {
      newErrors.minimumAllocation = 'Minimum allocation cannot be negative';
    }

    if (constraints.maximumPerGoal && constraints.maximumPerGoal < 0) {
      newErrors.maximumPerGoal = 'Maximum per goal cannot be negative';
    }

    if (
      constraints.minimumAllocation &&
      constraints.maximumPerGoal &&
      constraints.minimumAllocation > constraints.maximumPerGoal
    ) {
      newErrors.maximumPerGoal = 'Maximum per goal must be greater than minimum allocation';
    }

    if (constraints.minimumAllocation && amount < constraints.minimumAllocation) {
      newErrors.amount = 'Amount is less than minimum allocation requirement';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(
        amount,
        strategy,
        Object.keys(constraints).length > 0 ? constraints : undefined,
        source || undefined
      );
      
      // Reset form
      setAmount(0);
      setSource('');
      setConstraints({});
      setShowAdvanced(false);
      setErrors({});
    } catch (error: any) {
      setErrors({ submit: error.message || 'Failed to allocate funds' });
    }
  };

  const handleConstraintChange = (field: keyof AllocationConstraints, value: any) => {
    setConstraints(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear related errors
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const toggleGoalExclusion = (goalId: string) => {
    const currentExclusions = constraints.excludeGoals || [];
    const newExclusions = currentExclusions.includes(goalId)
      ? currentExclusions.filter(id => id !== goalId)
      : [...currentExclusions, goalId];
    
    handleConstraintChange('excludeGoals', newExclusions.length > 0 ? newExclusions : undefined);
  };

  const activeGoals = goals.filter(goal => goal.status !== 'completed');

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-semibold mb-6">Allocate Funds to Goals</h2>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Amount and Strategy Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">
              Amount to Allocate *
            </label>
            <input
              type="number"
              id="amount"
              value={amount}
              onChange={(e) => {
                setAmount(parseFloat(e.target.value) || 0);
                if (errors.amount) setErrors(prev => ({ ...prev, amount: '' }));
              }}
              placeholder="0.00"
              step="0.01"
              min="0"
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.amount ? 'border-red-500' : 'border-gray-300'
              }`}
              required
            />
            {errors.amount && <p className="text-red-500 text-xs mt-1">{errors.amount}</p>}
          </div>

          <div>
            <label htmlFor="strategy" className="block text-sm font-medium text-gray-700 mb-1">
              Allocation Strategy *
            </label>
            <select
              id="strategy"
              value={strategy}
              onChange={(e) => setStrategy(e.target.value as 'priority' | 'timeline' | 'balanced')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="priority">Priority-Based</option>
              <option value="timeline">Timeline-Based</option>
              <option value="balanced">Balanced</option>
            </select>
          </div>
        </div>

        {/* Source */}
        <div>
          <label htmlFor="source" className="block text-sm font-medium text-gray-700 mb-1">
            Source (Optional)
          </label>
          <input
            type="text"
            id="source"
            value={source}
            onChange={(e) => setSource(e.target.value)}
            placeholder="e.g., Salary, Bonus, Investment Return"
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Strategy Descriptions */}
        <div className="bg-gray-50 p-3 rounded-md">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Strategy Description:</h4>
          <p className="text-sm text-gray-600">
            {strategy === 'priority' && 'Allocates funds based on goal priority (high → medium → low)'}
            {strategy === 'timeline' && 'Allocates funds based on target dates (closer dates get priority)'}
            {strategy === 'balanced' && 'Distributes funds across all goals with intelligent weighting'}
          </p>
        </div>

        {/* Advanced Options Toggle */}
        <div>
          <button
            type="button"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="flex items-center text-blue-600 hover:text-blue-800 text-sm"
          >
            <svg 
              className={`w-4 h-4 mr-1 transform transition-transform ${showAdvanced ? 'rotate-90' : ''}`} 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
            Advanced Options
          </button>
        </div>

        {/* Advanced Options */}
        {showAdvanced && (
          <div className="space-y-4 border-t pt-4">
            {/* Constraints */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="minimumAllocation" className="block text-sm font-medium text-gray-700 mb-1">
                  Minimum Allocation per Goal
                </label>
                <input
                  type="number"
                  id="minimumAllocation"
                  value={constraints.minimumAllocation || ''}
                  onChange={(e) => handleConstraintChange('minimumAllocation', parseFloat(e.target.value) || undefined)}
                  placeholder="0.00"
                  step="0.01"
                  min="0"
                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.minimumAllocation ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.minimumAllocation && <p className="text-red-500 text-xs mt-1">{errors.minimumAllocation}</p>}
              </div>

              <div>
                <label htmlFor="maximumPerGoal" className="block text-sm font-medium text-gray-700 mb-1">
                  Maximum Allocation per Goal
                </label>
                <input
                  type="number"
                  id="maximumPerGoal"
                  value={constraints.maximumPerGoal || ''}
                  onChange={(e) => handleConstraintChange('maximumPerGoal', parseFloat(e.target.value) || undefined)}
                  placeholder="0.00"
                  step="0.01"
                  min="0"
                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.maximumPerGoal ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.maximumPerGoal && <p className="text-red-500 text-xs mt-1">{errors.maximumPerGoal}</p>}
              </div>
            </div>

            {/* Goal Exclusions */}
            {activeGoals.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Exclude Goals from Allocation
                </label>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {activeGoals.map(goal => (
                    <label key={goal.id} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={(constraints.excludeGoals || []).includes(goal.id)}
                        onChange={() => toggleGoalExclusion(goal.id)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700">
                        {goal.name} (${goal.currentAmount.toLocaleString()} / ${goal.targetAmount.toLocaleString()})
                      </span>
                    </label>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Error Message */}
        {errors.submit && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <p className="text-red-600 text-sm">{errors.submit}</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 pt-4">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
            >
              Cancel
            </button>
          )}
          <button
            type="submit"
            disabled={isLoading || activeGoals.length === 0}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isLoading ? 'Allocating...' : 'Allocate Funds'}
          </button>
        </div>

        {activeGoals.length === 0 && (
          <p className="text-gray-500 text-sm text-center">
            No active goals available for allocation. Create some goals first!
          </p>
        )}
      </form>
    </div>
  );
};

export default AllocationForm;
