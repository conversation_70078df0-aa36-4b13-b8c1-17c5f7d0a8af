'use client';

import React from 'react';
import { Goal } from '@/types/goal';

interface AllocationResult {
  goalId: string;
  amount: number;
  percentage: number;
}

interface AllocationResponse {
  totalAllocated: number;
  remainingAmount: number;
  allocations: AllocationResult[];
  strategy?: string;
}

interface AllocationResultsProps {
  results: AllocationResponse;
  goals: Goal[];
  onConfirm?: () => void;
  onCancel?: () => void;
  isConfirming?: boolean;
}

const AllocationResults: React.FC<AllocationResultsProps> = ({
  results,
  goals,
  onConfirm,
  onCancel,
  isConfirming = false
}) => {
  const getGoalById = (goalId: string) => {
    return goals.find(goal => goal.id === goalId);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getProgressAfterAllocation = (goal: Goal, allocation: AllocationResult) => {
    const newAmount = goal.currentAmount + allocation.amount;
    return (newAmount / goal.targetAmount) * 100;
  };

  const sortedAllocations = [...results.allocations].sort((a, b) => b.amount - a.amount);

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold">Allocation Results</h2>
        <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
          {results.strategy || 'Unknown'} Strategy
        </span>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-green-600">Total Allocated</p>
              <p className="text-2xl font-bold text-green-900">{formatCurrency(results.totalAllocated)}</p>
            </div>
          </div>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-blue-600">Goals Affected</p>
              <p className="text-2xl font-bold text-blue-900">{results.allocations.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="w-8 h-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Remaining</p>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(results.remainingAmount)}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Allocation Details */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Allocation Breakdown</h3>
        
        {sortedAllocations.length === 0 ? (
          <div className="text-center py-8">
            <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <p className="text-gray-500">No allocations were made</p>
          </div>
        ) : (
          <div className="space-y-3">
            {sortedAllocations.map((allocation) => {
              const goal = getGoalById(allocation.goalId);
              if (!goal) return null;

              const progressAfter = getProgressAfterAllocation(goal, allocation);
              const progressBefore = (goal.currentAmount / goal.targetAmount) * 100;

              return (
                <div key={allocation.goalId} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{goal.name}</h4>
                      <p className="text-sm text-gray-500">{goal.category}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-semibold text-green-600">
                        +{formatCurrency(allocation.amount)}
                      </p>
                      <p className="text-sm text-gray-500">
                        {allocation.percentage.toFixed(1)}% of total
                      </p>
                    </div>
                  </div>

                  {/* Progress Bars */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-xs text-gray-600">
                      <span>Before: {progressBefore.toFixed(1)}%</span>
                      <span>After: {progressAfter.toFixed(1)}%</span>
                    </div>
                    
                    {/* Before Progress */}
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-gray-400 h-2 rounded-full transition-all duration-300" 
                        style={{ width: `${Math.min(progressBefore, 100)}%` }}
                      />
                    </div>
                    
                    {/* After Progress */}
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-green-500 h-2 rounded-full transition-all duration-300" 
                        style={{ width: `${Math.min(progressAfter, 100)}%` }}
                      />
                    </div>
                  </div>

                  {/* Goal Details */}
                  <div className="mt-3 grid grid-cols-2 gap-4 text-xs text-gray-600">
                    <div>
                      <span className="font-medium">Current:</span> {formatCurrency(goal.currentAmount)}
                    </div>
                    <div>
                      <span className="font-medium">After:</span> {formatCurrency(goal.currentAmount + allocation.amount)}
                    </div>
                    <div>
                      <span className="font-medium">Target:</span> {formatCurrency(goal.targetAmount)}
                    </div>
                    <div>
                      <span className="font-medium">Remaining:</span> {formatCurrency(goal.targetAmount - goal.currentAmount - allocation.amount)}
                    </div>
                  </div>

                  {/* Completion Status */}
                  {progressAfter >= 100 && (
                    <div className="mt-2 flex items-center text-green-600">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span className="text-sm font-medium">Goal will be completed!</span>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Action Buttons */}
      {(onConfirm || onCancel) && (
        <div className="flex justify-end space-x-3 pt-6 border-t">
          {onCancel && (
            <button
              onClick={onCancel}
              className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
            >
              Cancel
            </button>
          )}
          {onConfirm && (
            <button
              onClick={onConfirm}
              disabled={isConfirming || sortedAllocations.length === 0}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isConfirming ? 'Confirming...' : 'Confirm Allocation'}
            </button>
          )}
        </div>
      )}

      {/* Warning for remaining amount */}
      {results.remainingAmount > 0 && (
        <div className="mt-4 bg-yellow-50 border border-yellow-200 rounded-md p-3">
          <div className="flex">
            <svg className="w-5 h-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <p className="text-sm text-yellow-700">
              <span className="font-medium">{formatCurrency(results.remainingAmount)}</span> could not be allocated. 
              This might be due to constraints or completed goals.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default AllocationResults;
