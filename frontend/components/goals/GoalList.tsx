'use client';

import React, { useState } from 'react';
import { Goal } from '@/types/goal';
import GoalCard from './GoalCard';

interface GoalListProps {
  goals: Goal[];
  isLoading: boolean;
  onEdit?: (goal: Goal) => void;
  onDelete?: (goalId: string) => void;
  onAllocate?: (goal: Goal) => void;
  showActions?: boolean;
  viewMode?: 'grid' | 'list';
  onViewModeChange?: (mode: 'grid' | 'list') => void;
}

const GoalList: React.FC<GoalListProps> = ({
  goals,
  isLoading,
  onEdit,
  onDelete,
  onAllocate,
  showActions = true,
  viewMode = 'grid',
  onViewModeChange
}) => {
  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null);

  const handleDelete = (goalId: string) => {
    if (deleteConfirm === goalId) {
      onDelete?.(goalId);
      setDeleteConfirm(null);
    } else {
      setDeleteConfirm(goalId);
      // Auto-cancel confirmation after 3 seconds
      setTimeout(() => setDeleteConfirm(null), 3000);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">Goals</h3>
        </div>
        <div className="animate-pulse">
          <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4' : 'space-y-4'}>
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-48 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (goals.length === 0) {
    return (
      <div className="text-center py-12">
        <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No goals found</h3>
        <p className="text-gray-500 mb-4">Start by creating your first financial goal to track your progress.</p>
      </div>
    );
  }

  // Group goals by status for better organization
  const groupedGoals = {
    active: goals.filter(goal => goal.status === 'active'),
    ahead: goals.filter(goal => goal.status === 'ahead'),
    behind: goals.filter(goal => goal.status === 'behind'),
    completed: goals.filter(goal => goal.status === 'completed'),
    paused: goals.filter(goal => goal.status === 'paused'),
  };

  const StatusSection: React.FC<{ 
    title: string; 
    goals: Goal[]; 
    statusColor: string;
    icon: React.ReactNode;
  }> = ({ title, goals, statusColor, icon }) => {
    if (goals.length === 0) return null;

    return (
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <div className={`p-2 rounded-lg ${statusColor} mr-3`}>
            {icon}
          </div>
          <h4 className="text-lg font-medium text-gray-900">
            {title} ({goals.length})
          </h4>
        </div>
        
        <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4' : 'space-y-4'}>
          {goals.map((goal) => (
            <div key={goal.id} className={viewMode === 'list' ? 'max-w-none' : ''}>
              <GoalCard
                goal={goal}
                onEdit={onEdit}
                onDelete={deleteConfirm === goal.id ? () => handleDelete(goal.id) : () => setDeleteConfirm(goal.id)}
                onAllocate={onAllocate}
                showActions={showActions}
              />
              {deleteConfirm === goal.id && (
                <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                  Click delete again to confirm removal of "{goal.name}"
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header with View Mode Toggle */}
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Goals ({goals.length})</h3>
        
        {onViewModeChange && (
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">View:</span>
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => onViewModeChange('grid')}
                className={`px-3 py-1 rounded-md text-sm transition-colors ${
                  viewMode === 'grid'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
              </button>
              <button
                onClick={() => onViewModeChange('list')}
                className={`px-3 py-1 rounded-md text-sm transition-colors ${
                  viewMode === 'list'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Goals by Status */}
      <div>
        <StatusSection
          title="Active Goals"
          goals={groupedGoals.active}
          statusColor="bg-blue-100 text-blue-600"
          icon={
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          }
        />

        <StatusSection
          title="Ahead of Schedule"
          goals={groupedGoals.ahead}
          statusColor="bg-green-100 text-green-600"
          icon={
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          }
        />

        <StatusSection
          title="Behind Schedule"
          goals={groupedGoals.behind}
          statusColor="bg-red-100 text-red-600"
          icon={
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          }
        />

        <StatusSection
          title="Paused Goals"
          goals={groupedGoals.paused}
          statusColor="bg-gray-100 text-gray-600"
          icon={
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          }
        />

        <StatusSection
          title="Completed Goals"
          goals={groupedGoals.completed}
          statusColor="bg-green-100 text-green-600"
          icon={
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          }
        />
      </div>
    </div>
  );
};

export default GoalList;
