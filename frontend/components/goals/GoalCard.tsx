import React from 'react';
import { Goal } from '@/types/goal';

interface GoalCardProps {
  goal: Goal;
  onEdit?: (goal: Goal) => void;
  onDelete?: (goalId: string) => void;
  onAllocate?: (goal: Goal) => void;
  showActions?: boolean;
}

// Helper function to get the appropriate color class based on priority
function getPriorityClass(priority: string): string {
  switch (priority) {
    case 'high':
      return 'bg-red-100 text-red-800';
    case 'medium':
      return 'bg-yellow-100 text-yellow-800';
    case 'low':
      return 'bg-blue-100 text-blue-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

// Helper function to get the appropriate color class based on status
function getStatusClass(status: string): string {
  switch (status) {
    case 'completed':
      return 'bg-green-100 text-green-800';
    case 'behind':
      return 'bg-red-100 text-red-800';
    case 'ahead':
      return 'bg-blue-100 text-blue-800';
    case 'paused':
      return 'bg-gray-100 text-gray-800';
    case 'active':
    default:
      return 'bg-green-100 text-green-800';
  }
}

export default function GoalCard({
  goal,
  onEdit,
  onDelete,
  onAllocate,
  showActions = true
}: GoalCardProps) {
  const progress = (goal.currentAmount / goal.targetAmount) * 100;

  return (
    <div className="bg-white rounded-lg shadow p-4">
      <div className="flex justify-between items-start">
        <h3 className="font-bold text-lg">{goal.name}</h3>
        <span className={`px-2 py-1 text-xs rounded ${getPriorityClass(goal.priority)}`}>
          {goal.priority}
        </span>
      </div>

      <div className="mt-2">
        <p className="text-sm text-gray-600">
          Target: ₱{goal.targetAmount.toLocaleString()}
        </p>
        <p className="text-sm text-gray-600">
          Current: ₱{goal.currentAmount.toLocaleString()}
        </p>
      </div>

      <div className="mt-3">
        <div className="w-full bg-gray-200 rounded-full h-2.5">
          <div
            className="bg-blue-600 h-2.5 rounded-full"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
        <p className="text-xs text-right mt-1 text-gray-600">
          {progress.toFixed(0)}% complete
        </p>
      </div>

      <div className="mt-3 flex justify-between items-center">
        <span className={`px-2 py-1 text-xs rounded ${getStatusClass(goal.status)}`}>
          {goal.status}
        </span>
        <span className="text-xs text-gray-500">
          Due: {new Date(goal.targetDate).toLocaleDateString()}
        </span>
      </div>

      {/* Goal Actions */}
      {showActions && (
        <div className="mt-4 flex justify-between items-center">
          <div className="flex space-x-2">
            {onAllocate && goal.status !== 'completed' && (
              <button
                onClick={() => onAllocate(goal)}
                className="px-3 py-1 bg-green-100 text-green-700 hover:bg-green-200 rounded-md text-sm transition-colors"
                title="Allocate funds to this goal"
              >
                <svg className="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Allocate
              </button>
            )}
          </div>

          <div className="flex space-x-2">
            {onEdit && (
              <button
                onClick={() => onEdit(goal)}
                className="text-blue-600 hover:text-blue-800 transition-colors"
                title="Edit goal"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </button>
            )}

            {onDelete && (
              <button
                onClick={() => onDelete(goal.id)}
                className="text-red-600 hover:text-red-800 transition-colors"
                title="Delete goal"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            )}
          </div>
        </div>
      )}

      {/* Goal Progress Details */}
      <div className="mt-3 text-xs text-gray-500 space-y-1">
        <div className="flex justify-between">
          <span>Remaining: ${(goal.targetAmount - goal.currentAmount).toLocaleString()}</span>
          <span>Progress: {progress.toFixed(1)}%</span>
        </div>
        {goal.status !== 'completed' && (
          <div className="flex justify-between">
            <span>Days left: {Math.max(0, Math.ceil((new Date(goal.targetDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)))}</span>
            <span>Monthly needed: ${((goal.targetAmount - goal.currentAmount) / Math.max(1, Math.ceil((new Date(goal.targetDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24 * 30)))).toFixed(0)}</span>
          </div>
        )}
      </div>
    </div>
  );
}
