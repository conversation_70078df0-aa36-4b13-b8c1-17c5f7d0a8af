'use client';

// Temporarily disabled authentication for Challenge 4 component development
// Authentication is a bonus task according to TODO.md, not a core requirement
// import { useAuth } from '@/hooks/useAuth';

export default function Header() {
  // Temporarily disabled auth context usage
  // const { user, logout } = useAuth();

  return (
    <header className="bg-white shadow">
      <div className="mx-auto px-4 py-4 sm:px-6 lg:px-8 flex justify-between items-center">
        <h1 className="text-xl font-semibold text-gray-900">Dashboard</h1>
        <div className="flex items-center">
          <span className="mr-4 text-gray-700">
            Demo User (Auth Disabled)
          </span>
          <button
            onClick={() => {
              // Temporarily disabled logout functionality
              console.log('Logout clicked - Auth disabled for component development');
            }}
            className="text-sm px-3 py-1 bg-gray-400 text-white rounded cursor-not-allowed"
            disabled
          >
            Logout (Disabled)
          </button>
        </div>
      </div>
    </header>
  );
}
