import { renderHook, act } from '@testing-library/react';
import { useGoals } from '@/hooks/useGoals';
import { api } from '@/lib/api';

// Mock the API
jest.mock('@/lib/api');
const mockedApi = api as jest.Mocked<typeof api>;

// Mock goal data
const mockGoals = [
  {
    id: '1',
    name: 'Emergency Fund',
    targetAmount: 10000,
    currentAmount: 2500,
    startDate: '2024-01-01',
    targetDate: '2024-12-31',
    priority: 'high' as const,
    category: 'Savings',
    status: 'active' as const,
    progress: 25,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z'
  },
  {
    id: '2',
    name: 'Vacation Fund',
    targetAmount: 5000,
    currentAmount: 5000,
    startDate: '2024-01-01',
    targetDate: '2024-06-30',
    priority: 'medium' as const,
    category: 'Travel',
    status: 'completed' as const,
    progress: 100,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-06-30T00:00:00Z'
  },
  {
    id: '3',
    name: 'New Car',
    targetAmount: 25000,
    currentAmount: 8000,
    startDate: '2024-01-01',
    targetDate: '2025-01-01',
    priority: 'low' as const,
    category: 'Transportation',
    status: 'behind' as const,
    progress: 32,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z'
  }
];

describe('useGoals Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockedApi.get.mockResolvedValue({ data: { data: mockGoals } });
  });

  it('should fetch goals on mount', async () => {
    const { result } = renderHook(() => useGoals());

    expect(result.current.isLoading).toBe(true);
    expect(mockedApi.get).toHaveBeenCalledWith('/goals');

    // Wait for the effect to complete
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    expect(result.current.isLoading).toBe(false);
    expect(result.current.goals).toEqual(mockGoals);
    expect(result.current.error).toBe(null);
  });

  it('should calculate summary correctly', async () => {
    const { result } = renderHook(() => useGoals());

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    const { summary } = result.current;

    expect(summary.total).toBe(3);
    expect(summary.completed).toBe(1);
    expect(summary.active).toBe(1);
    expect(summary.behind).toBe(1);
    expect(summary.ahead).toBe(0);
    expect(summary.paused).toBe(0);
    expect(summary.totalTargetAmount).toBe(40000); // 10000 + 5000 + 25000
    expect(summary.totalCurrentAmount).toBe(15500); // 2500 + 5000 + 8000
    expect(summary.overallProgress).toBeCloseTo(38.75, 2); // (15500 / 40000) * 100
  });

  it('should filter goals by status', async () => {
    const { result } = renderHook(() => useGoals());

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    // Filter by completed status
    act(() => {
      result.current.setFilters({
        status: 'completed'
      });
    });

    expect(result.current.filteredGoals).toHaveLength(1);
    expect(result.current.filteredGoals[0].status).toBe('completed');
  });

  it('should filter goals by priority', async () => {
    const { result } = renderHook(() => useGoals());

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    // Filter by high priority
    act(() => {
      result.current.setFilters({
        priority: 'high'
      });
    });

    expect(result.current.filteredGoals).toHaveLength(1);
    expect(result.current.filteredGoals[0].priority).toBe('high');
  });

  it('should filter goals by search term', async () => {
    const { result } = renderHook(() => useGoals());

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    // Search for "emergency"
    act(() => {
      result.current.setFilters({
        search: 'emergency'
      });
    });

    expect(result.current.filteredGoals).toHaveLength(1);
    expect(result.current.filteredGoals[0].name).toBe('Emergency Fund');
  });

  it('should sort goals correctly', async () => {
    const { result } = renderHook(() => useGoals());

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    // Sort by target amount descending
    act(() => {
      result.current.updateSort('targetAmount', 'desc');
    });

    const amounts = result.current.filteredGoals.map(g => g.targetAmount);
    expect(amounts).toEqual([25000, 10000, 5000]);

    // Sort by target amount ascending
    act(() => {
      result.current.updateSort('targetAmount', 'asc');
    });

    const amountsAsc = result.current.filteredGoals.map(g => g.targetAmount);
    expect(amountsAsc).toEqual([5000, 10000, 25000]);
  });

  it('should create a new goal', async () => {
    const { result } = renderHook(() => useGoals());

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    const newGoal = {
      name: 'House Down Payment',
      targetAmount: 50000,
      startDate: '2024-02-01',
      targetDate: '2026-02-01',
      priority: 'high' as const,
      category: 'Housing'
    };

    const createdGoal = {
      ...newGoal,
      id: '4',
      currentAmount: 0,
      status: 'active' as const,
      progress: 0,
      createdAt: '2024-02-01T00:00:00Z',
      updatedAt: '2024-02-01T00:00:00Z'
    };

    mockedApi.post.mockResolvedValue({ data: createdGoal });

    await act(async () => {
      await result.current.createGoal(newGoal);
    });

    expect(mockedApi.post).toHaveBeenCalledWith('/goals', newGoal);
    expect(result.current.goals).toHaveLength(4);
    expect(result.current.goals.find(g => g.id === '4')).toEqual(createdGoal);
  });

  it('should update a goal', async () => {
    const { result } = renderHook(() => useGoals());

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    const updates = { currentAmount: 3000 };
    const updatedGoal = { ...mockGoals[0], ...updates };

    mockedApi.put.mockResolvedValue({ data: updatedGoal });

    await act(async () => {
      await result.current.updateGoal('1', updates);
    });

    expect(mockedApi.put).toHaveBeenCalledWith('/goals/1', updates);
    expect(result.current.goals.find(g => g.id === '1')?.currentAmount).toBe(3000);
  });

  it('should delete a goal', async () => {
    const { result } = renderHook(() => useGoals());

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    mockedApi.delete.mockResolvedValue({});

    await act(async () => {
      await result.current.deleteGoal('1');
    });

    expect(mockedApi.delete).toHaveBeenCalledWith('/goals/1');
    expect(result.current.goals).toHaveLength(2);
    expect(result.current.goals.find(g => g.id === '1')).toBeUndefined();
  });

  it('should reset filters correctly', async () => {
    const { result } = renderHook(() => useGoals());

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    // Set some filters
    act(() => {
      result.current.setFilters({
        status: 'active',
        priority: 'high',
        search: 'test'
      });
    });

    expect(result.current.filters.status).toBe('active');
    expect(result.current.filters.priority).toBe('high');
    expect(result.current.filters.search).toBe('test');

    // Reset filters
    act(() => {
      result.current.resetFilters();
    });

    expect(result.current.filters.status).toBeUndefined();
    expect(result.current.filters.priority).toBeUndefined();
    expect(result.current.filters.search).toBeUndefined();
  });

  it('should extract available categories', async () => {
    const { result } = renderHook(() => useGoals());

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    const categories = result.current.availableCategories;
    expect(categories).toEqual(['Savings', 'Transportation', 'Travel']);
  });

  it('should group goals by status', async () => {
    const { result } = renderHook(() => useGoals());

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    const { goalsByStatus } = result.current;

    expect(goalsByStatus.active).toHaveLength(1);
    expect(goalsByStatus.completed).toHaveLength(1);
    expect(goalsByStatus.behind).toHaveLength(1);
    expect(goalsByStatus.ahead).toHaveLength(0);
    expect(goalsByStatus.paused).toHaveLength(0);
  });

  it('should group goals by priority', async () => {
    const { result } = renderHook(() => useGoals());

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    const { goalsByPriority } = result.current;

    expect(goalsByPriority.high).toHaveLength(1);
    expect(goalsByPriority.medium).toHaveLength(1);
    expect(goalsByPriority.low).toHaveLength(1);
  });
});
