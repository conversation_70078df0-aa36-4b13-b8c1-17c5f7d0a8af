import { renderHook, act } from '@testing-library/react';
import { useAllocations } from '@/hooks/useAllocations';
import { api } from '@/lib/api';

// Mock the API
jest.mock('@/lib/api');
const mockedApi = api as jest.Mocked<typeof api>;

describe('useAllocations Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with correct default state', () => {
    const { result } = renderHook(() => useAllocations());

    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe(null);
    expect(result.current.lastResult).toBe(null);
    expect(result.current.hasAllocations).toBe(false);
  });

  it('should allocate funds successfully', async () => {
    const { result } = renderHook(() => useAllocations());

    const mockResponse = {
      totalAllocated: 1000,
      remainingAmount: 0,
      allocations: [
        {
          id: '1',
          goalId: 'goal1',
          amount: 600,
          source: 'manual',
          date: '2024-01-15',
          createdAt: '2024-01-15T10:00:00Z'
        },
        {
          id: '2',
          goalId: 'goal2',
          amount: 400,
          source: 'manual',
          date: '2024-01-15',
          createdAt: '2024-01-15T10:00:00Z'
        }
      ],
      strategy: 'priority'
    };

    mockedApi.post.mockResolvedValue({ data: mockResponse });

    let allocationResult;
    await act(async () => {
      allocationResult = await result.current.allocateFunds(1000, 'priority');
    });

    expect(mockedApi.post).toHaveBeenCalledWith('/allocations', {
      amount: 1000,
      strategy: 'priority',
      constraints: undefined,
      source: undefined
    });

    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe(null);
    expect(result.current.lastResult).toEqual(mockResponse);
    expect(result.current.hasAllocations).toBe(true);
    expect(allocationResult).toEqual(mockResponse);
  });

  it('should validate allocation request and throw error for invalid amount', async () => {
    const { result } = renderHook(() => useAllocations());

    await act(async () => {
      await expect(result.current.allocateFunds(0, 'priority')).rejects.toThrow(
        'Allocation amount must be greater than 0'
      );
    });

    expect(result.current.error).toBe('Allocation amount must be greater than 0');
    expect(result.current.isLoading).toBe(false);
  });

  it('should validate allocation request and throw error for invalid strategy', async () => {
    const { result } = renderHook(() => useAllocations());

    await act(async () => {
      await expect(result.current.allocateFunds(1000, 'invalid' as any)).rejects.toThrow(
        'Invalid allocation strategy'
      );
    });

    expect(result.current.error).toBe('Invalid allocation strategy');
  });

  it('should validate constraints correctly', async () => {
    const { result } = renderHook(() => useAllocations());

    const constraints = {
      minimumAllocation: 100,
      maximumPerGoal: 50 // This should cause an error
    };

    await act(async () => {
      await expect(result.current.allocateFunds(1000, 'priority', constraints)).rejects.toThrow(
        'Minimum allocation cannot be greater than maximum per goal'
      );
    });

    expect(result.current.error).toBe('Minimum allocation cannot be greater than maximum per goal');
  });

  it('should validate minimum allocation requirement', async () => {
    const { result } = renderHook(() => useAllocations());

    const constraints = {
      minimumAllocation: 1500
    };

    await act(async () => {
      await expect(result.current.allocateFunds(1000, 'priority', constraints)).rejects.toThrow(
        'Allocation amount is less than minimum allocation requirement'
      );
    });

    expect(result.current.error).toBe('Allocation amount is less than minimum allocation requirement');
  });

  it('should get goal allocations successfully', async () => {
    const { result } = renderHook(() => useAllocations());

    const mockAllocations = [
      {
        id: '1',
        goalId: 'goal1',
        amount: 500,
        source: 'manual',
        date: '2024-01-15',
        createdAt: '2024-01-15T10:00:00Z'
      }
    ];

    mockedApi.get.mockResolvedValue({ data: { data: mockAllocations } });

    let goalAllocations;
    await act(async () => {
      goalAllocations = await result.current.getGoalAllocations('goal1');
    });

    expect(mockedApi.get).toHaveBeenCalledWith('/goals/goal1/allocations');
    expect(goalAllocations).toEqual(mockAllocations);
    expect(result.current.error).toBe(null);
  });

  it('should get all allocations successfully', async () => {
    const { result } = renderHook(() => useAllocations());

    const mockAllocations = [
      {
        id: '1',
        goalId: 'goal1',
        amount: 500,
        source: 'manual',
        date: '2024-01-15',
        createdAt: '2024-01-15T10:00:00Z'
      },
      {
        id: '2',
        goalId: 'goal2',
        amount: 300,
        source: 'automatic',
        date: '2024-01-14',
        createdAt: '2024-01-14T10:00:00Z'
      }
    ];

    mockedApi.get.mockResolvedValue({ data: { data: mockAllocations } });

    let allAllocations;
    await act(async () => {
      allAllocations = await result.current.getAllocations();
    });

    expect(mockedApi.get).toHaveBeenCalledWith('/allocations');
    expect(allAllocations).toEqual(mockAllocations);
    expect(result.current.allocationHistory.records).toEqual(mockAllocations);
    expect(result.current.allocationHistory.totalAllocated).toBe(800);
    expect(result.current.allocationHistory.allocationCount).toBe(2);
    expect(result.current.allocationHistory.averageAllocation).toBe(400);
  });

  it('should clear error state', () => {
    const { result } = renderHook(() => useAllocations());

    // Set an error first
    act(() => {
      result.current.allocateFunds(0, 'priority').catch(() => {});
    });

    act(() => {
      result.current.clearError();
    });

    expect(result.current.error).toBe(null);
  });

  it('should reset state correctly', () => {
    const { result } = renderHook(() => useAllocations());

    // First set some state
    act(() => {
      result.current.allocateFunds(1000, 'priority').catch(() => {});
    });

    act(() => {
      result.current.resetState();
    });

    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe(null);
    expect(result.current.lastResult).toBe(null);
    expect(result.current.hasAllocations).toBe(false);
  });

  it('should validate allocation request correctly', () => {
    const { result } = renderHook(() => useAllocations());

    // Valid request
    const validErrors = result.current.validateAllocationRequest(1000, 'priority');
    expect(validErrors).toEqual([]);

    // Invalid amount
    const invalidAmountErrors = result.current.validateAllocationRequest(0, 'priority');
    expect(invalidAmountErrors).toContain('Allocation amount must be greater than 0');

    // Invalid strategy
    const invalidStrategyErrors = result.current.validateAllocationRequest(1000, 'invalid');
    expect(invalidStrategyErrors).toContain('Invalid allocation strategy');

    // Invalid constraints
    const invalidConstraintsErrors = result.current.validateAllocationRequest(1000, 'priority', {
      minimumAllocation: 100,
      maximumPerGoal: 50
    });
    expect(invalidConstraintsErrors).toContain('Minimum allocation cannot be greater than maximum per goal');
  });

  it('should check if can allocate correctly', () => {
    const { result } = renderHook(() => useAllocations());

    // Valid allocation
    expect(result.current.canAllocate(1000)).toBe(true);

    // Invalid amount
    expect(result.current.canAllocate(0)).toBe(false);
    expect(result.current.canAllocate(-100)).toBe(false);

    // With constraints
    expect(result.current.canAllocate(1000, { minimumAllocation: 500 })).toBe(true);
    expect(result.current.canAllocate(1000, { minimumAllocation: 1500 })).toBe(false);
  });

  it('should handle API errors correctly', async () => {
    const { result } = renderHook(() => useAllocations());

    const apiError = new Error('Network error');
    mockedApi.post.mockRejectedValue(apiError);

    await act(async () => {
      await expect(result.current.allocateFunds(1000, 'priority')).rejects.toThrow('Network error');
    });

    expect(result.current.error).toBe('Network error');
    expect(result.current.isLoading).toBe(false);
  });
});
