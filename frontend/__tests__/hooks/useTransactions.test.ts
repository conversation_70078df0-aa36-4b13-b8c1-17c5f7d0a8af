import { renderHook, act } from '@testing-library/react';
import { useTransactions } from '@/hooks/useTransactions';
import { api } from '@/lib/api';

// Mock the API
jest.mock('@/lib/api');
const mockedApi = api as jest.Mocked<typeof api>;

// Mock transaction data
const mockTransactions = [
  {
    id: '1',
    date: '2024-01-15',
    merchant: 'Grocery Store',
    amount: 150.50,
    type: 'expense' as const,
    category: 'Food',
    description: 'Weekly groceries',
    status: 'completed' as const,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    date: '2024-01-16',
    merchant: 'Salary',
    amount: 5000.00,
    type: 'income' as const,
    category: 'Salary',
    description: 'Monthly salary',
    status: 'completed' as const,
    createdAt: '2024-01-16T09:00:00Z',
    updatedAt: '2024-01-16T09:00:00Z'
  },
  {
    id: '3',
    date: '2024-01-17',
    merchant: 'Gas Station',
    amount: 75.25,
    type: 'expense' as const,
    category: 'Transportation',
    description: 'Gas fill-up',
    status: 'completed' as const,
    createdAt: '2024-01-17T08:00:00Z',
    updatedAt: '2024-01-17T08:00:00Z'
  }
];

describe('useTransactions Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockedApi.get.mockResolvedValue({ data: { data: mockTransactions } });
  });

  it('should fetch transactions on mount', async () => {
    const { result } = renderHook(() => useTransactions());

    expect(result.current.isLoading).toBe(true);
    expect(mockedApi.get).toHaveBeenCalledWith('/transactions');

    // Wait for the effect to complete
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    expect(result.current.isLoading).toBe(false);
    expect(result.current.transactions).toEqual(mockTransactions);
    expect(result.current.error).toBe(null);
  });

  it('should calculate summary correctly', async () => {
    const { result } = renderHook(() => useTransactions());

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    const { summary } = result.current;
    
    expect(summary.income).toBe(5000.00);
    expect(summary.expenses).toBe(225.75); // 150.50 + 75.25
    expect(summary.balance).toBe(4774.25); // 5000.00 - 225.75
    expect(summary.transactionCount).toBe(3);
    expect(summary.averageTransaction).toBeCloseTo(1741.92, 2); // (5000 + 150.50 + 75.25) / 3
  });

  it('should filter transactions by type', async () => {
    const { result } = renderHook(() => useTransactions());

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    // Filter by expense type
    act(() => {
      result.current.setFilters({
        types: ['expense'],
        dateRange: { startDate: null, endDate: null },
        search: ''
      });
    });

    expect(result.current.filteredTransactions).toHaveLength(2);
    expect(result.current.filteredTransactions.every(t => t.type === 'expense')).toBe(true);
  });

  it('should filter transactions by search term', async () => {
    const { result } = renderHook(() => useTransactions());

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    // Search for "grocery"
    act(() => {
      result.current.setFilters({
        types: [],
        dateRange: { startDate: null, endDate: null },
        search: 'grocery'
      });
    });

    expect(result.current.filteredTransactions).toHaveLength(1);
    expect(result.current.filteredTransactions[0].merchant).toBe('Grocery Store');
  });

  it('should handle pagination correctly', async () => {
    const { result } = renderHook(() => useTransactions());

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    // Set page size to 2
    act(() => {
      result.current.updatePagination({ limit: 2, page: 1 });
    });

    expect(result.current.paginatedTransactions).toHaveLength(2);
    expect(result.current.hasNextPage).toBe(true);
    expect(result.current.hasPrevPage).toBe(false);
    expect(result.current.totalPages).toBe(2);

    // Go to page 2
    act(() => {
      result.current.updatePagination({ page: 2 });
    });

    expect(result.current.paginatedTransactions).toHaveLength(1);
    expect(result.current.hasNextPage).toBe(false);
    expect(result.current.hasPrevPage).toBe(true);
  });

  it('should sort transactions correctly', async () => {
    const { result } = renderHook(() => useTransactions());

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    // Sort by amount descending
    act(() => {
      result.current.updateSort('amount', 'desc');
    });

    const amounts = result.current.filteredTransactions.map(t => t.amount);
    expect(amounts).toEqual([5000.00, 150.50, 75.25]);

    // Sort by amount ascending
    act(() => {
      result.current.updateSort('amount', 'asc');
    });

    const amountsAsc = result.current.filteredTransactions.map(t => t.amount);
    expect(amountsAsc).toEqual([75.25, 150.50, 5000.00]);
  });

  it('should reset filters correctly', async () => {
    const { result } = renderHook(() => useTransactions());

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    // Set some filters
    act(() => {
      result.current.setFilters({
        types: ['expense'],
        dateRange: { startDate: '2024-01-01', endDate: '2024-01-31' },
        search: 'test'
      });
    });

    expect(result.current.filters.types).toEqual(['expense']);
    expect(result.current.filters.search).toBe('test');

    // Reset filters
    act(() => {
      result.current.resetFilters();
    });

    expect(result.current.filters.types).toEqual([]);
    expect(result.current.filters.search).toBe('');
    expect(result.current.filters.dateRange.startDate).toBe(null);
    expect(result.current.filters.dateRange.endDate).toBe(null);
  });

  it('should extract available categories', async () => {
    const { result } = renderHook(() => useTransactions());

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    const categories = result.current.availableCategories;
    expect(categories).toEqual(['Food', 'Salary', 'Transportation']);
  });
});
