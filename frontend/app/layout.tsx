import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
// Removed auth provider for now to get the app started

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'FinTrack',
  description: 'Financial tracking and goal management',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        {children}
      </body>
    </html>
  );
}