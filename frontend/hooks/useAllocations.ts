import { useState, useCallback, useMemo } from 'react';
import { api } from '@/lib/api';
import { AllocationConstraints } from '@/types/goal';

interface AllocationResult {
  goalId: string;
  amount: number;
  percentage: number;
}

interface AllocationRecord {
  id: string;
  goalId: string;
  amount: number;
  source: string;
  date: string;
  createdAt: string;
}

interface AllocationResponse {
  totalAllocated: number;
  remainingAmount: number;
  allocations: AllocationRecord[];
  strategy?: string;
}

interface AllocationHistory {
  records: AllocationRecord[];
  totalAllocated: number;
  allocationCount: number;
  averageAllocation: number;
  lastAllocation?: AllocationRecord;
}

interface AllocationState {
  isLoading: boolean;
  error: string | null;
  lastResult: AllocationResponse | null;
  history: AllocationRecord[];
}

// Enhanced allocations hook with comprehensive state management
export function useAllocations() {
  const [state, setState] = useState<AllocationState>({
    isLoading: false,
    error: null,
    lastResult: null,
    history: []
  });

  // Enhanced allocate funds with validation and state management
  const allocateFunds = useCallback(async (
    amount: number,
    strategy: 'priority' | 'timeline' | 'balanced',
    constraints?: AllocationConstraints,
    source?: string
  ): Promise<AllocationResponse> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Validate request
      if (!amount || amount <= 0) {
        throw new Error('Allocation amount must be greater than 0');
      }

      if (!['priority', 'timeline', 'balanced'].includes(strategy)) {
        throw new Error('Invalid allocation strategy');
      }

      // Validate constraints
      if (constraints) {
        const { minimumAllocation, maximumPerGoal } = constraints;

        if (minimumAllocation && minimumAllocation < 0) {
          throw new Error('Minimum allocation cannot be negative');
        }

        if (maximumPerGoal && maximumPerGoal < 0) {
          throw new Error('Maximum per goal cannot be negative');
        }

        if (minimumAllocation && maximumPerGoal && minimumAllocation > maximumPerGoal) {
          throw new Error('Minimum allocation cannot be greater than maximum per goal');
        }

        if (minimumAllocation && amount < minimumAllocation) {
          throw new Error('Allocation amount is less than minimum allocation requirement');
        }
      }

      const response = await api.post('/allocations', {
        amount,
        strategy,
        constraints,
        source
      });

      const result: AllocationResponse = response.data;

      setState(prev => ({
        ...prev,
        isLoading: false,
        error: null,
        lastResult: result,
        history: [...(result.allocations || []), ...prev.history]
      }));

      return result;
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to allocate funds';
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));
      throw new Error(errorMessage);
    }
  }, []);

  // Enhanced get goal allocations
  const getGoalAllocations = useCallback(async (goalId: string): Promise<AllocationRecord[]> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await api.get(`/goals/${goalId}/allocations`);
      const goalAllocations = response.data.data;

      setState(prev => ({ ...prev, isLoading: false, error: null }));
      return goalAllocations;
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to fetch goal allocations';
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));
      throw new Error(errorMessage);
    }
  }, []);

  // Get all allocation history
  const getAllocations = useCallback(async (): Promise<AllocationRecord[]> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await api.get('/allocations');
      const allAllocations = response.data.data;

      setState(prev => ({
        ...prev,
        isLoading: false,
        error: null,
        history: allAllocations
      }));

      return allAllocations;
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to fetch allocations';
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));
      throw new Error(errorMessage);
    }
  }, []);

  // Utility functions
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  const resetState = useCallback(() => {
    setState({
      isLoading: false,
      error: null,
      lastResult: null,
      history: []
    });
  }, []);

  // Memoized allocation history analysis
  const allocationHistory = useMemo((): AllocationHistory => {
    const records = state.history;
    const totalAllocated = records.reduce((sum, record) => sum + record.amount, 0);
    const allocationCount = records.length;
    const averageAllocation = allocationCount > 0 ? totalAllocated / allocationCount : 0;
    const lastAllocation = records.length > 0 ? records[0] : undefined;

    return {
      records,
      totalAllocated: Math.round(totalAllocated * 100) / 100,
      allocationCount,
      averageAllocation: Math.round(averageAllocation * 100) / 100,
      lastAllocation
    };
  }, [state.history]);

  // Validation helper
  const validateAllocationRequest = useCallback((
    amount: number,
    strategy: string,
    constraints?: AllocationConstraints
  ): string[] => {
    const errors: string[] = [];

    if (!amount || amount <= 0) {
      errors.push('Allocation amount must be greater than 0');
    }

    if (!['priority', 'timeline', 'balanced'].includes(strategy)) {
      errors.push('Invalid allocation strategy');
    }

    if (constraints) {
      const { minimumAllocation, maximumPerGoal } = constraints;

      if (minimumAllocation && minimumAllocation < 0) {
        errors.push('Minimum allocation cannot be negative');
      }

      if (maximumPerGoal && maximumPerGoal < 0) {
        errors.push('Maximum per goal cannot be negative');
      }

      if (minimumAllocation && maximumPerGoal && minimumAllocation > maximumPerGoal) {
        errors.push('Minimum allocation cannot be greater than maximum per goal');
      }

      if (minimumAllocation && amount < minimumAllocation) {
        errors.push('Allocation amount is less than minimum allocation requirement');
      }
    }

    return errors;
  }, []);

  return {
    // State
    isLoading: state.isLoading,
    error: state.error,
    lastResult: state.lastResult,

    // Actions
    allocateFunds,
    getGoalAllocations,
    getAllocations,
    clearError,
    resetState,

    // Computed data
    allocationHistory,

    // Quick access
    hasAllocations: state.history.length > 0,
    lastAllocation: state.lastResult,

    // Utility functions
    canAllocate: useCallback((amount: number, constraints?: AllocationConstraints) => {
      return validateAllocationRequest(amount, 'priority', constraints).length === 0;
    }, [validateAllocationRequest]),

    validateAllocationRequest
  };
}
