import { useState, useEffect, useCallback, useMemo } from 'react';
import { api } from '@/lib/api';
import { Goal } from '@/types/goal';

interface GoalFilters {
  status?: 'active' | 'completed' | 'paused' | 'behind' | 'ahead';
  priority?: 'low' | 'medium' | 'high';
  category?: string;
  search?: string;
}

interface GoalSortConfig {
  field: keyof Goal | 'progress' | 'remainingAmount' | 'daysUntilTarget';
  direction: 'asc' | 'desc';
}

interface GoalSummary {
  total: number;
  completed: number;
  active: number;
  behind: number;
  ahead: number;
  paused: number;
  totalTargetAmount: number;
  totalCurrentAmount: number;
  overallProgress: number;
  averageProgress: number;
}

interface EnhancedGoal extends Goal {
  progressPercentage?: number;
  remainingAmount?: number;
  daysUntilTarget?: number;
  expectedProgress?: number;
  isOnTrack?: boolean;
  isCompleted?: boolean;
  isOverdue?: boolean;
  monthlySavingsNeeded?: number;
  timeProgress?: number;
}

// Enhanced goals hook with comprehensive state management
export function useGoals() {
  const [goals, setGoals] = useState<EnhancedGoal[]>([]);
  const [filteredGoals, setFilteredGoals] = useState<EnhancedGoal[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<GoalFilters>({});
  const [sortConfig, setSortConfig] = useState<GoalSortConfig>({
    field: 'targetDate',
    direction: 'asc'
  });

  // Fetch goals
  useEffect(() => {
    const fetchGoals = async () => {
      setIsLoading(true);
      try {
        const response = await api.get('/goals');
        setGoals(response.data.data);
        setError(null);
      } catch (err: any) {
        setError(err.message || 'Failed to fetch goals');
        setGoals([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchGoals();
  }, []);

  // Memoized filtering logic
  const applyFilters = useCallback((goals: EnhancedGoal[], filters: GoalFilters): EnhancedGoal[] => {
    return goals.filter(goal => {
      // Status filter
      if (filters.status && goal.status !== filters.status) {
        return false;
      }

      // Priority filter
      if (filters.priority && goal.priority !== filters.priority) {
        return false;
      }

      // Category filter
      if (filters.category && goal.category !== filters.category) {
        return false;
      }

      // Search filter (name, category)
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        const searchableText = [
          goal.name,
          goal.category || '',
        ].join(' ').toLowerCase();

        if (!searchableText.includes(searchTerm)) {
          return false;
        }
      }

      return true;
    });
  }, []);

  // Memoized sorting logic
  const applySorting = useCallback((goals: EnhancedGoal[], sort: GoalSortConfig): EnhancedGoal[] => {
    return [...goals].sort((a, b) => {
      let aValue: any;
      let bValue: any;

      // Handle computed fields
      if (sort.field === 'progress') {
        aValue = a.progressPercentage || 0;
        bValue = b.progressPercentage || 0;
      } else if (sort.field === 'remainingAmount') {
        aValue = a.remainingAmount || 0;
        bValue = b.remainingAmount || 0;
      } else if (sort.field === 'daysUntilTarget') {
        aValue = a.daysUntilTarget || 0;
        bValue = b.daysUntilTarget || 0;
      } else {
        aValue = a[sort.field as keyof Goal];
        bValue = b[sort.field as keyof Goal];
      }

      // Handle date sorting
      if (sort.field === 'targetDate' || sort.field === 'startDate' || sort.field === 'createdAt' || sort.field === 'updatedAt') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      // Handle string sorting (case insensitive)
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) {
        return sort.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sort.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }, []);

  // Apply filters and sorting when data changes
  useEffect(() => {
    const filtered = applyFilters(goals, filters);
    const sorted = applySorting(filtered, sortConfig);
    setFilteredGoals(sorted);
  }, [goals, filters, sortConfig, applyFilters, applySorting]);

  // Calculate goal summary
  const summary = useMemo((): GoalSummary => {
    const total = goals.length;
    const completed = goals.filter(g => g.status === 'completed').length;
    const active = goals.filter(g => g.status === 'active').length;
    const behind = goals.filter(g => g.status === 'behind').length;
    const ahead = goals.filter(g => g.status === 'ahead').length;
    const paused = goals.filter(g => g.status === 'paused').length;

    const totalTargetAmount = goals.reduce((sum, g) => sum + g.targetAmount, 0);
    const totalCurrentAmount = goals.reduce((sum, g) => sum + g.currentAmount, 0);
    const overallProgress = totalTargetAmount > 0 ? (totalCurrentAmount / totalTargetAmount) * 100 : 0;

    const progressSum = goals.reduce((sum, g) => sum + (g.progressPercentage || (g.currentAmount / g.targetAmount) * 100), 0);
    const averageProgress = total > 0 ? progressSum / total : 0;

    return {
      total,
      completed,
      active,
      behind,
      ahead,
      paused,
      totalTargetAmount: Math.round(totalTargetAmount * 100) / 100,
      totalCurrentAmount: Math.round(totalCurrentAmount * 100) / 100,
      overallProgress: Math.round(overallProgress * 100) / 100,
      averageProgress: Math.round(averageProgress * 100) / 100,
    };
  }, [goals]);

  const createGoal = async (goalData: Omit<Goal, 'id' | 'currentAmount' | 'progress' | 'createdAt' | 'updatedAt'>) => {
    try {
      const response = await api.post('/goals', goalData);
      setGoals([...goals, response.data]);
      return response.data;
    } catch (err: any) {
      throw new Error(err.message || 'Failed to create goal');
    }
  };

  const updateGoal = async (id: string, goalData: Partial<Goal>) => {
    try {
      const response = await api.put(`/goals/${id}`, goalData);
      setGoals(goals.map(goal => goal.id === id ? response.data : goal));
      return response.data;
    } catch (err: any) {
      throw new Error(err.message || 'Failed to update goal');
    }
  };

  const deleteGoal = async (id: string) => {
    try {
      await api.delete(`/goals/${id}`);
      setGoals(goals.filter(goal => goal.id !== id));
    } catch (err: any) {
      throw new Error(err.message || 'Failed to delete goal');
    }
  };

  // Utility functions
  const refreshGoals = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await api.get('/goals');
      setGoals(response.data.data);
      setError(null);
    } catch (err: any) {
      setError(err.message || 'Failed to refresh goals');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const resetFilters = useCallback(() => {
    setFilters({});
  }, []);

  const updateSort = useCallback((field: GoalSortConfig['field'], direction?: 'asc' | 'desc') => {
    setSortConfig(prev => ({
      field,
      direction: direction || (prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc')
    }));
  }, []);

  return {
    // Data
    goals,
    filteredGoals,

    // State
    isLoading,
    error,
    filters,
    sortConfig,
    summary,

    // Actions
    setFilters,
    setSortConfig,
    createGoal,
    updateGoal,
    deleteGoal,
    refreshGoals,
    resetFilters,
    updateSort,

    // Computed values
    availableCategories: useMemo(() => {
      const categories = new Set(goals.map(g => g.category).filter(Boolean));
      return Array.from(categories).sort();
    }, [goals]),

    // Goal status counts for quick access
    goalsByStatus: useMemo(() => ({
      active: goals.filter(g => g.status === 'active'),
      completed: goals.filter(g => g.status === 'completed'),
      behind: goals.filter(g => g.status === 'behind'),
      ahead: goals.filter(g => g.status === 'ahead'),
      paused: goals.filter(g => g.status === 'paused'),
    }), [goals]),

    // Goals by priority for quick access
    goalsByPriority: useMemo(() => ({
      high: goals.filter(g => g.priority === 'high'),
      medium: goals.filter(g => g.priority === 'medium'),
      low: goals.filter(g => g.priority === 'low'),
    }), [goals])
  };
}
