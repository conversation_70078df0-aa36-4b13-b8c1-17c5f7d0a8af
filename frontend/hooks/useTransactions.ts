import { useState, useEffect, useCallback, useMemo } from 'react';
import { api } from '@/lib/api';
import { Transaction, TransactionFilters, TransactionSummaryType } from '@/types/transaction';

interface SortConfig {
  field: keyof Transaction;
  direction: 'asc' | 'desc';
}

interface PaginationConfig {
  page: number;
  limit: number;
  total: number;
}

interface CategoryBreakdown {
  category: string;
  amount: number;
  count: number;
  percentage: number;
}

interface EnhancedSummary extends TransactionSummaryType {
  transactionCount: number;
  averageTransaction: number;
  categoryBreakdown: CategoryBreakdown[];
}

// Enhanced transaction hook with comprehensive state management
export function useTransactions() {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [filteredTransactions, setFilteredTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<TransactionFilters>({
    types: [],
    dateRange: {
      startDate: null,
      endDate: null
    },
    search: '',
    category: undefined
  });
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    field: 'date',
    direction: 'desc'
  });
  const [pagination, setPagination] = useState<PaginationConfig>({
    page: 1,
    limit: 20,
    total: 0
  });
  const [summary, setSummary] = useState<EnhancedSummary>({
    income: 0,
    expenses: 0,
    balance: 0,
    transactionCount: 0,
    averageTransaction: 0,
    categoryBreakdown: []
  });

  // Fetch transactions
  useEffect(() => {
    const fetchTransactions = async () => {
      setIsLoading(true);
      try {
        const response = await api.get('/transactions');
        setTransactions(response.data.data);
        setError(null);
      } catch (err: any) {
        setError(err.message || 'Failed to fetch transactions');
        setTransactions([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTransactions();
  }, []);

  // Memoized filtering logic
  const applyFilters = useCallback((transactions: Transaction[], filters: TransactionFilters): Transaction[] => {
    return transactions.filter(transaction => {
      // Type filter
      if (filters.types.length > 0 && !filters.types.includes(transaction.type)) {
        return false;
      }

      // Category filter
      if (filters.category && transaction.category !== filters.category) {
        return false;
      }

      // Date range filter
      if (filters.dateRange.startDate) {
        const transactionDate = new Date(transaction.date);
        const startDate = new Date(filters.dateRange.startDate);
        if (transactionDate < startDate) {
          return false;
        }
      }

      if (filters.dateRange.endDate) {
        const transactionDate = new Date(transaction.date);
        const endDate = new Date(filters.dateRange.endDate);
        endDate.setHours(23, 59, 59, 999); // Include the entire end date
        if (transactionDate > endDate) {
          return false;
        }
      }

      // Search filter (merchant, description, category)
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        const searchableText = [
          transaction.merchant,
          transaction.description || '',
          transaction.category,
        ].join(' ').toLowerCase();

        if (!searchableText.includes(searchTerm)) {
          return false;
        }
      }

      return true;
    });
  }, []);

  // Memoized sorting logic
  const applySorting = useCallback((transactions: Transaction[], sort: SortConfig): Transaction[] => {
    return [...transactions].sort((a, b) => {
      let aValue: any = a[sort.field];
      let bValue: any = b[sort.field];

      // Handle date sorting
      if (sort.field === 'date' || sort.field === 'createdAt' || sort.field === 'updatedAt') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      // Handle string sorting (case insensitive)
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) {
        return sort.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sort.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }, []);

  // Memoized summary calculation
  const calculateSummary = useCallback((transactions: Transaction[]): EnhancedSummary => {
    const income = transactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0);

    const expenses = transactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0);

    const balance = income - expenses;
    const transactionCount = transactions.length;
    const averageTransaction = transactionCount > 0 ?
      transactions.reduce((sum, t) => sum + t.amount, 0) / transactionCount : 0;

    // Calculate category breakdown
    const categoryMap = new Map<string, { amount: number; count: number }>();

    transactions.forEach(transaction => {
      const existing = categoryMap.get(transaction.category) || { amount: 0, count: 0 };
      categoryMap.set(transaction.category, {
        amount: existing.amount + transaction.amount,
        count: existing.count + 1,
      });
    });

    const totalAmount = transactions.reduce((sum, t) => sum + t.amount, 0);
    const categoryBreakdown: CategoryBreakdown[] = Array.from(categoryMap.entries()).map(([category, data]) => ({
      category,
      amount: data.amount,
      count: data.count,
      percentage: totalAmount > 0 ? (data.amount / totalAmount) * 100 : 0,
    })).sort((a, b) => b.amount - a.amount);

    return {
      income: Math.round(income * 100) / 100,
      expenses: Math.round(expenses * 100) / 100,
      balance: Math.round(balance * 100) / 100,
      transactionCount,
      averageTransaction: Math.round(averageTransaction * 100) / 100,
      categoryBreakdown,
    };
  }, []);

  // Apply filters and sorting when data or filters change
  useEffect(() => {
    const filtered = applyFilters(transactions, filters);
    const sorted = applySorting(filtered, sortConfig);
    const calculatedSummary = calculateSummary(filtered);

    setFilteredTransactions(sorted);
    setSummary(calculatedSummary);
    setPagination(prev => ({
      ...prev,
      total: filtered.length,
      page: Math.min(prev.page, Math.ceil(filtered.length / prev.limit) || 1),
    }));
  }, [transactions, filters, sortConfig, applyFilters, applySorting, calculateSummary]);

  // Memoized paginated transactions
  const paginatedTransactions = useMemo(() => {
    const startIndex = (pagination.page - 1) * pagination.limit;
    const endIndex = startIndex + pagination.limit;
    return filteredTransactions.slice(startIndex, endIndex);
  }, [filteredTransactions, pagination.page, pagination.limit]);

  // CRUD operations
  const createTransaction = useCallback(async (transactionData: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      setIsLoading(true);
      const response = await api.post('/transactions', transactionData);
      const newTransaction = response.data;

      setTransactions(prev => [newTransaction, ...prev]);
      setError(null);
      return newTransaction;
    } catch (err: any) {
      setError(err.message || 'Failed to create transaction');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updateTransaction = useCallback(async (id: string, updates: Partial<Transaction>) => {
    try {
      setIsLoading(true);
      const response = await api.put(`/transactions/${id}`, updates);
      const updatedTransaction = response.data;

      setTransactions(prev =>
        prev.map(t => t.id === id ? updatedTransaction : t)
      );
      setError(null);
      return updatedTransaction;
    } catch (err: any) {
      setError(err.message || 'Failed to update transaction');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const deleteTransaction = useCallback(async (id: string) => {
    try {
      setIsLoading(true);
      await api.delete(`/transactions/${id}`);

      setTransactions(prev => prev.filter(t => t.id !== id));
      setError(null);
    } catch (err: any) {
      setError(err.message || 'Failed to delete transaction');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Utility functions
  const refreshTransactions = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await api.get('/transactions');
      setTransactions(response.data.data);
      setError(null);
    } catch (err: any) {
      setError(err.message || 'Failed to refresh transactions');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const resetFilters = useCallback(() => {
    setFilters({
      types: [],
      dateRange: {
        startDate: null,
        endDate: null
      },
      search: '',
      category: undefined
    });
    setPagination(prev => ({ ...prev, page: 1 }));
  }, []);

  const updateSort = useCallback((field: keyof Transaction, direction?: 'asc' | 'desc') => {
    setSortConfig(prev => ({
      field,
      direction: direction || (prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc')
    }));
    setPagination(prev => ({ ...prev, page: 1 }));
  }, []);

  const updatePagination = useCallback((updates: Partial<PaginationConfig>) => {
    setPagination(prev => ({ ...prev, ...updates }));
  }, []);

  return {
    // Data
    transactions,
    filteredTransactions,
    paginatedTransactions,

    // State
    isLoading,
    error,
    filters,
    sortConfig,
    pagination,
    summary,

    // Actions
    setFilters,
    setSortConfig,
    setPagination,
    createTransaction,
    updateTransaction,
    deleteTransaction,
    refreshTransactions,
    resetFilters,
    updateSort,
    updatePagination,

    // Computed values
    hasNextPage: pagination.page < Math.ceil(pagination.total / pagination.limit),
    hasPrevPage: pagination.page > 1,
    totalPages: Math.ceil(pagination.total / pagination.limit),

    // Categories for filtering
    availableCategories: useMemo(() => {
      const categories = new Set(transactions.map(t => t.category));
      return Array.from(categories).sort();
    }, [transactions])
  };
}
