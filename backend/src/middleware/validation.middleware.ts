import { Request, Response, NextFunction } from 'express';
import { body, param, query, validationResult } from 'express-validator';

// Middleware to handle validation errors
export const handleValidationErrors = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      message: 'Validation failed',
      errors: errors.array().map(error => ({
        field: error.type === 'field' ? error.path : 'unknown',
        message: error.msg,
        value: error.type === 'field' ? error.value : undefined
      }))
    });
  }
  next();
};

// Goal validation rules
export const validateCreateGoal = [
  body('name')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Goal name must be between 1 and 100 characters'),
  
  body('targetAmount')
    .isFloat({ min: 0.01 })
    .withMessage('Target amount must be a positive number'),
  
  body('targetDate')
    .isISO8601()
    .toDate()
    .custom((value) => {
      if (new Date(value) <= new Date()) {
        throw new Error('Target date must be in the future');
      }
      return true;
    }),
  
  body('priority')
    .isIn(['low', 'medium', 'high'])
    .withMessage('Priority must be low, medium, or high'),
  
  body('category')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Category must be less than 50 characters'),
  
  handleValidationErrors
];

export const validateUpdateGoal = [
  param('id')
    .isMongoId()
    .withMessage('Invalid goal ID'),
  
  body('name')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Goal name must be between 1 and 100 characters'),
  
  body('targetAmount')
    .optional()
    .isFloat({ min: 0.01 })
    .withMessage('Target amount must be a positive number'),
  
  body('targetDate')
    .optional()
    .isISO8601()
    .toDate()
    .custom((value, { req }) => {
      if (value && new Date(value) <= new Date()) {
        throw new Error('Target date must be in the future');
      }
      return true;
    }),
  
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high'])
    .withMessage('Priority must be low, medium, or high'),
  
  body('status')
    .optional()
    .isIn(['active', 'completed', 'paused', 'behind', 'ahead'])
    .withMessage('Status must be active, completed, paused, behind, or ahead'),
  
  body('category')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Category must be less than 50 characters'),
  
  handleValidationErrors
];

// Transaction validation rules
export const validateCreateTransaction = [
  body('amount')
    .isFloat({ min: 0.01 })
    .withMessage('Amount must be a positive number'),
  
  body('type')
    .isIn(['income', 'expense', 'transfer'])
    .withMessage('Type must be income, expense, or transfer'),
  
  body('category')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Category must be between 1 and 50 characters'),
  
  body('merchant')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Merchant name must be less than 100 characters'),
  
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description must be less than 500 characters'),
  
  body('date')
    .optional()
    .isISO8601()
    .toDate()
    .withMessage('Date must be a valid ISO date'),
  
  handleValidationErrors
];

// Allocation validation rules
export const validateAllocateFunds = [
  body('amount')
    .isFloat({ min: 0.01 })
    .withMessage('Allocation amount must be a positive number'),
  
  body('strategy')
    .isIn(['priority', 'timeline', 'balanced'])
    .withMessage('Strategy must be priority, timeline, or balanced'),
  
  body('constraints.minimumAllocation')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Minimum allocation must be a non-negative number'),
  
  body('constraints.maximumPerGoal')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Maximum per goal must be a non-negative number'),
  
  body('constraints.excludeGoals')
    .optional()
    .isArray()
    .withMessage('Exclude goals must be an array')
    .custom((value) => {
      if (Array.isArray(value)) {
        const invalidIds = value.filter(id => typeof id !== 'string' || id.length !== 24);
        if (invalidIds.length > 0) {
          throw new Error('All excluded goal IDs must be valid MongoDB ObjectIds');
        }
      }
      return true;
    }),
  
  body('source')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Source must be less than 100 characters'),
  
  handleValidationErrors
];

// Query validation rules
export const validateGoalQuery = [
  query('status')
    .optional()
    .isIn(['active', 'completed', 'paused', 'behind', 'ahead'])
    .withMessage('Status must be active, completed, paused, behind, or ahead'),
  
  query('priority')
    .optional()
    .isIn(['low', 'medium', 'high'])
    .withMessage('Priority must be low, medium, or high'),
  
  query('sortBy')
    .optional()
    .isIn(['name', 'targetAmount', 'targetDate', 'priority', 'createdAt', 'progress'])
    .withMessage('Sort field must be name, targetAmount, targetDate, priority, createdAt, or progress'),
  
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc'),
  
  handleValidationErrors
];

export const validateTransactionQuery = [
  query('type')
    .optional()
    .isIn(['income', 'expense', 'transfer'])
    .withMessage('Type must be income, expense, or transfer'),
  
  query('category')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Category must be less than 50 characters'),
  
  query('startDate')
    .optional()
    .isISO8601()
    .toDate()
    .withMessage('Start date must be a valid ISO date'),
  
  query('endDate')
    .optional()
    .isISO8601()
    .toDate()
    .withMessage('End date must be a valid ISO date'),
  
  query('minAmount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Minimum amount must be a non-negative number'),
  
  query('maxAmount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Maximum amount must be a non-negative number'),
  
  handleValidationErrors
];

// Parameter validation
export const validateMongoId = [
  param('id')
    .isMongoId()
    .withMessage('Invalid ID format'),
  
  handleValidationErrors
];

export const validateGoalId = [
  param('goalId')
    .isMongoId()
    .withMessage('Invalid goal ID format'),
  
  handleValidationErrors
];

// Business logic validation
export const validateGoalOwnership = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { Goal } = await import('../models/goal.model');
    const userId = req.user._id;
    const goalId = req.params.id || req.params.goalId;
    
    const goal = await Goal.findOne({ _id: goalId, userId });
    if (!goal) {
      return res.status(404).json({ message: 'Goal not found or access denied' });
    }
    
    req.goal = goal; // Attach goal to request for use in controller
    next();
  } catch (error) {
    res.status(500).json({ message: 'Server error during validation' });
  }
};

export const validateAllocationConstraints = (req: Request, res: Response, next: NextFunction) => {
  const { constraints } = req.body;
  
  if (constraints) {
    const { minimumAllocation, maximumPerGoal } = constraints;
    
    // Check if minimum allocation is not greater than maximum per goal
    if (minimumAllocation && maximumPerGoal && minimumAllocation > maximumPerGoal) {
      return res.status(400).json({
        message: 'Minimum allocation cannot be greater than maximum per goal'
      });
    }
    
    // Check if allocation amount is sufficient for minimum allocation
    if (minimumAllocation && req.body.amount < minimumAllocation) {
      return res.status(400).json({
        message: 'Allocation amount is less than minimum allocation requirement'
      });
    }
  }
  
  next();
};
