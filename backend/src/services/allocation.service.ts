import { Goal } from '../models/goal.model';

// Define the structure for allocation options
export interface AllocationOptions {
  minimumAllocation?: number;
  maximumPerGoal?: number;
  excludeGoals?: string[];
}

// Define the structure for allocation results
export interface AllocationResult {
  goalId: string;
  amount: number;
  percentage: number;
}

// Base Strategy Interface
export interface AllocationStrategy {
  allocate(amount: number, goals: any[], options?: AllocationOptions): AllocationResult[];
}

// Priority-Based Allocation Strategy
export class PriorityBasedAllocation implements AllocationStrategy {
  allocate(amount: number, goals: any[], options?: AllocationOptions): AllocationResult[] {
    if (!goals || goals.length === 0 || amount <= 0) {
      return [];
    }

    // Filter out excluded goals and goals that are already completed
    const eligibleGoals = goals.filter(goal => {
      const isExcluded = options?.excludeGoals?.includes(goal._id.toString());
      const isCompleted = goal.currentAmount >= goal.targetAmount;
      return !isExcluded && !isCompleted;
    });

    if (eligibleGoals.length === 0) {
      return [];
    }

    // Group goals by priority (high -> medium -> low)
    const priorityOrder = ['high', 'medium', 'low'];
    const goalsByPriority = priorityOrder.map(priority =>
      eligibleGoals.filter(goal => goal.priority === priority)
    );

    let remainingAmount = amount;
    const allocations: AllocationResult[] = [];

    // Process each priority level
    for (const priorityGoals of goalsByPriority) {
      if (priorityGoals.length === 0 || remainingAmount <= 0) continue;

      // Calculate remaining amounts needed for each goal
      const goalsWithNeeds = priorityGoals.map(goal => ({
        ...goal,
        remainingNeeded: Math.max(0, goal.targetAmount - goal.currentAmount),
        progressRatio: goal.currentAmount / goal.targetAmount
      })).filter(goal => goal.remainingNeeded > 0);

      if (goalsWithNeeds.length === 0) continue;

      // For same priority goals, distribute proportionally based on progress
      // Goals further from completion (lower progress) get more allocation
      const totalInverseProgress = goalsWithNeeds.reduce((sum, goal) =>
        sum + (1 - goal.progressRatio), 0
      );

      let priorityAllocations: AllocationResult[] = [];

      if (totalInverseProgress === 0) {
        // If all goals have same progress, distribute equally
        const equalAmount = remainingAmount / goalsWithNeeds.length;
        priorityAllocations = goalsWithNeeds.map(goal => ({
          goalId: goal._id.toString(),
          amount: this.applyConstraints(equalAmount, goal, options),
          percentage: 0 // Will be calculated later
        }));
      } else {
        // Distribute based on inverse progress (less complete goals get more)
        priorityAllocations = goalsWithNeeds.map(goal => {
          const weight = (1 - goal.progressRatio) / totalInverseProgress;
          const proposedAmount = remainingAmount * weight;
          return {
            goalId: goal._id.toString(),
            amount: this.applyConstraints(proposedAmount, goal, options),
            percentage: 0 // Will be calculated later
          };
        });
      }

      // Add to main allocations and update remaining amount
      allocations.push(...priorityAllocations);
      const allocatedInThisPriority = priorityAllocations.reduce((sum, alloc) => sum + alloc.amount, 0);
      remainingAmount -= allocatedInThisPriority;

      // If we've allocated all funds, break
      if (remainingAmount <= 0) break;
    }

    // Filter out zero allocations and calculate percentages
    const validAllocations = allocations.filter(alloc => alloc.amount > 0);
    const totalAllocated = validAllocations.reduce((sum, alloc) => sum + alloc.amount, 0);
    validAllocations.forEach(allocation => {
      allocation.percentage = totalAllocated > 0 ? (allocation.amount / amount) * 100 : 0;
    });

    return validAllocations;
  }

  private applyConstraints(proposedAmount: number, goal: any, options?: AllocationOptions): number {
    let finalAmount = proposedAmount;

    // Don't exceed what's needed to complete the goal
    const remainingNeeded = goal.targetAmount - goal.currentAmount;
    if (finalAmount > remainingNeeded) {
      finalAmount = remainingNeeded;
    }

    // Apply minimum allocation constraint - if we can't meet minimum, return 0
    if (options?.minimumAllocation && finalAmount > 0 && finalAmount < options.minimumAllocation) {
      return 0; // Can't meet minimum allocation, so don't allocate anything
    }

    // Apply maximum per goal constraint
    if (options?.maximumPerGoal && finalAmount > options.maximumPerGoal) {
      finalAmount = options.maximumPerGoal;
    }

    // Ensure non-negative
    return Math.max(0, finalAmount);
  }
}

// Timeline-Based Allocation Strategy
export class TimelineBasedAllocation implements AllocationStrategy {
  allocate(amount: number, goals: any[], options?: AllocationOptions): AllocationResult[] {
    if (!goals || goals.length === 0 || amount <= 0) {
      return [];
    }

    // Filter out excluded goals and goals that are already completed
    const eligibleGoals = goals.filter(goal => {
      const isExcluded = options?.excludeGoals?.includes(goal._id.toString());
      const isCompleted = goal.currentAmount >= goal.targetAmount;
      return !isExcluded && !isCompleted;
    });

    if (eligibleGoals.length === 0) {
      return [];
    }

    const currentDate = new Date();

    // Calculate urgency scores for each goal
    const goalsWithUrgency = eligibleGoals.map(goal => {
      const targetDate = new Date(goal.targetDate);
      const daysUntilTarget = Math.max(1, Math.ceil((targetDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24)));
      const progressRatio = goal.currentAmount / goal.targetAmount;
      const remainingNeeded = Math.max(0, goal.targetAmount - goal.currentAmount);

      // Calculate urgency: combination of time pressure and progress deficit
      // Goals with closer deadlines and lower progress get higher urgency
      const timeUrgency = 1 / daysUntilTarget; // Higher for closer deadlines
      const progressUrgency = 1 - progressRatio; // Higher for less complete goals

      // Combined urgency score (weighted combination)
      const urgencyScore = (timeUrgency * 0.6) + (progressUrgency * 0.4);

      return {
        ...goal,
        daysUntilTarget,
        progressRatio,
        remainingNeeded,
        urgencyScore
      };
    }).filter(goal => goal.remainingNeeded > 0);

    if (goalsWithUrgency.length === 0) {
      return [];
    }

    // Calculate total urgency for proportional distribution
    const totalUrgency = goalsWithUrgency.reduce((sum, goal) => sum + goal.urgencyScore, 0);

    // Allocate funds based on urgency scores
    const allocations: AllocationResult[] = goalsWithUrgency.map(goal => {
      const weight = goal.urgencyScore / totalUrgency;
      const proposedAmount = amount * weight;

      return {
        goalId: goal._id.toString(),
        amount: this.applyConstraints(proposedAmount, goal, options),
        percentage: 0 // Will be calculated later
      };
    });

    // Handle rounding and ensure we don't over-allocate
    const totalAllocated = allocations.reduce((sum, alloc) => sum + alloc.amount, 0);

    // Redistribute any remaining amount due to constraints
    const remainingAmount = amount - totalAllocated;
    if (remainingAmount > 0) {
      this.redistributeRemaining(remainingAmount, allocations, goalsWithUrgency, options);
    }

    // Calculate final percentages
    const finalTotalAllocated = allocations.reduce((sum, alloc) => sum + alloc.amount, 0);
    allocations.forEach(allocation => {
      allocation.percentage = finalTotalAllocated > 0 ? (allocation.amount / amount) * 100 : 0;
    });

    return allocations.filter(alloc => alloc.amount > 0);
  }

  private applyConstraints(proposedAmount: number, goal: any, options?: AllocationOptions): number {
    let finalAmount = proposedAmount;

    // Apply minimum allocation constraint
    if (options?.minimumAllocation && finalAmount > 0 && finalAmount < options.minimumAllocation) {
      finalAmount = options.minimumAllocation;
    }

    // Apply maximum per goal constraint
    if (options?.maximumPerGoal && finalAmount > options.maximumPerGoal) {
      finalAmount = options.maximumPerGoal;
    }

    // Don't exceed what's needed to complete the goal
    if (finalAmount > goal.remainingNeeded) {
      finalAmount = goal.remainingNeeded;
    }

    // Ensure non-negative
    return Math.max(0, finalAmount);
  }

  private redistributeRemaining(remainingAmount: number, allocations: AllocationResult[], goals: any[], options?: AllocationOptions): void {
    // Find goals that can still receive more allocation
    const eligibleForMore = allocations.map((alloc, index) => ({
      allocation: alloc,
      goal: goals[index],
      canReceiveMore: alloc.amount < goals[index].remainingNeeded &&
        (!options?.maximumPerGoal || alloc.amount < options.maximumPerGoal)
    })).filter(item => item.canReceiveMore);

    if (eligibleForMore.length === 0) return;

    // Distribute remaining amount proportionally among eligible goals
    const totalEligibleUrgency = eligibleForMore.reduce((sum, item) => sum + item.goal.urgencyScore, 0);

    eligibleForMore.forEach(item => {
      const weight = item.goal.urgencyScore / totalEligibleUrgency;
      const additionalAmount = remainingAmount * weight;
      const maxAdditional = Math.min(
        additionalAmount,
        item.goal.remainingNeeded - item.allocation.amount,
        options?.maximumPerGoal ? options.maximumPerGoal - item.allocation.amount : Infinity
      );

      item.allocation.amount += Math.max(0, maxAdditional);
    });
  }
}

// Balanced Allocation Strategy
export class BalancedAllocation implements AllocationStrategy {
  allocate(amount: number, goals: any[], options?: AllocationOptions): AllocationResult[] {
    if (!goals || goals.length === 0 || amount <= 0) {
      return [];
    }

    // Filter out excluded goals and goals that are already completed
    const eligibleGoals = goals.filter(goal => {
      const isExcluded = options?.excludeGoals?.includes(goal._id.toString());
      const isCompleted = goal.currentAmount >= goal.targetAmount;
      return !isExcluded && !isCompleted;
    });

    if (eligibleGoals.length === 0) {
      return [];
    }

    const currentDate = new Date();

    // Calculate balanced scores for each goal
    const goalsWithScores = eligibleGoals.map(goal => {
      const targetDate = new Date(goal.targetDate);
      const daysUntilTarget = Math.max(1, Math.ceil((targetDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24)));
      const progressRatio = goal.currentAmount / goal.targetAmount;
      const remainingNeeded = Math.max(0, goal.targetAmount - goal.currentAmount);

      // Priority weight (high=3, medium=2, low=1)
      const priorityWeight = goal.priority === 'high' ? 3 : goal.priority === 'medium' ? 2 : 1;

      // Timeline urgency (closer deadlines get higher scores)
      const timelineUrgency = 1 / Math.sqrt(daysUntilTarget); // Diminishing returns for very close deadlines

      // Progress deficit (goals behind schedule get higher scores)
      const progressDeficit = 1 - progressRatio;

      // Schedule status bonus (goals marked as 'behind' get extra weight)
      const scheduleBonus = goal.status === 'behind' ? 1.5 : goal.status === 'ahead' ? 0.8 : 1.0;

      // Balanced score combines all factors
      // Priority: 30%, Timeline: 25%, Progress: 25%, Schedule: 20%
      const balancedScore = (
        (priorityWeight * 0.30) +
        (timelineUrgency * 0.25) +
        (progressDeficit * 0.25) +
        (scheduleBonus * 0.20)
      );

      return {
        ...goal,
        daysUntilTarget,
        progressRatio,
        remainingNeeded,
        priorityWeight,
        timelineUrgency,
        progressDeficit,
        scheduleBonus,
        balancedScore
      };
    }).filter(goal => goal.remainingNeeded > 0);

    if (goalsWithScores.length === 0) {
      return [];
    }

    // Ensure all goals get some allocation (balanced approach)
    // Start with base allocation, then distribute remaining based on scores
    const baseAllocationPerGoal = Math.min(
      amount * 0.1, // 10% of total amount as base
      amount / goalsWithScores.length * 0.5 // Or 50% of equal distribution
    );

    let remainingAmount = amount;
    const allocations: AllocationResult[] = [];

    // First pass: Give base allocation to all goals
    goalsWithScores.forEach(goal => {
      const baseAmount = Math.min(baseAllocationPerGoal, goal.remainingNeeded);
      const constrainedAmount = this.applyConstraints(baseAmount, goal, options);

      allocations.push({
        goalId: goal._id.toString(),
        amount: constrainedAmount,
        percentage: 0 // Will be calculated later
      });

      remainingAmount -= constrainedAmount;
    });

    // Second pass: Distribute remaining amount based on balanced scores
    if (remainingAmount > 0) {
      const totalScore = goalsWithScores.reduce((sum, goal) => sum + goal.balancedScore, 0);

      goalsWithScores.forEach((goal, index) => {
        const weight = goal.balancedScore / totalScore;
        const additionalAmount = remainingAmount * weight;

        // Find current allocation for this goal
        const currentAllocation = allocations[index];
        const maxAdditional = Math.min(
          additionalAmount,
          goal.remainingNeeded - currentAllocation.amount,
          options?.maximumPerGoal ? options.maximumPerGoal - currentAllocation.amount : Infinity
        );

        if (maxAdditional > 0) {
          currentAllocation.amount += maxAdditional;
        }
      });
    }

    // Calculate final percentages
    const totalAllocated = allocations.reduce((sum, alloc) => sum + alloc.amount, 0);
    allocations.forEach(allocation => {
      allocation.percentage = totalAllocated > 0 ? (allocation.amount / amount) * 100 : 0;
    });

    return allocations.filter(alloc => alloc.amount > 0);
  }

  private applyConstraints(proposedAmount: number, goal: any, options?: AllocationOptions): number {
    let finalAmount = proposedAmount;

    // Apply minimum allocation constraint
    if (options?.minimumAllocation && finalAmount > 0 && finalAmount < options.minimumAllocation) {
      finalAmount = options.minimumAllocation;
    }

    // Apply maximum per goal constraint
    if (options?.maximumPerGoal && finalAmount > options.maximumPerGoal) {
      finalAmount = options.maximumPerGoal;
    }

    // Don't exceed what's needed to complete the goal
    const remainingNeeded = goal.remainingNeeded || (goal.targetAmount - goal.currentAmount);
    if (finalAmount > remainingNeeded) {
      finalAmount = remainingNeeded;
    }

    // Ensure non-negative
    return Math.max(0, finalAmount);
  }
}

// Allocation Service that uses the Strategy Pattern
export class AllocationService {
  private strategy: AllocationStrategy;

  constructor(strategy: AllocationStrategy) {
    this.strategy = strategy;
  }

  setStrategy(strategy: AllocationStrategy): void {
    this.strategy = strategy;
  }

  async allocateFunds(amount: number, userId: string, options?: AllocationOptions): Promise<{
    allocations: AllocationResult[];
    totalAllocated: number;
    remainingAmount: number;
  }> {
    // Fetch eligible goals for the user
    const goals = await Goal.find({
      userId,
      status: { $in: ['active', 'behind'] },
      ...(options?.excludeGoals ? { _id: { $nin: options.excludeGoals } } : {}),
    });

    // Skip allocation if no eligible goals
    if (goals.length === 0) {
      return {
        allocations: [],
        totalAllocated: 0,
        remainingAmount: amount,
      };
    }

    // Perform allocation using the current strategy
    const allocations = this.strategy.allocate(amount, goals, options);

    // Calculate total allocated amount
    const totalAllocated = allocations.reduce((sum, allocation) => sum + allocation.amount, 0);

    return {
      allocations,
      totalAllocated,
      remainingAmount: amount - totalAllocated,
    };
  }
}
