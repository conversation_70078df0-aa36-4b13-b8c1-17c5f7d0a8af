import mongoose, { Document } from 'mongoose';

export type GoalPriority = 'high' | 'medium' | 'low';
export type GoalStatus = 'active' | 'completed' | 'behind' | 'ahead' | 'paused';

export interface IGoal extends Document {
  userId: mongoose.Types.ObjectId;
  name: string;
  targetAmount: number;
  currentAmount: number;
  startDate: Date;
  targetDate: Date;
  priority: GoalPriority;
  category: string;
  status: GoalStatus;
  createdAt: Date;
  updatedAt: Date;
}

const goalSchema = new mongoose.Schema<IGoal>(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    name: {
      type: String,
      required: [true, 'Goal name is required'],
      trim: true,
      minlength: [1, 'Goal name must be at least 1 character'],
      maxlength: [100, 'Goal name must be less than 100 characters']
    },
    targetAmount: {
      type: Number,
      required: [true, 'Target amount is required'],
      min: [0.01, 'Target amount must be greater than 0'],
      validate: {
        validator: function (value: number) {
          return value > 0;
        },
        message: 'Target amount must be a positive number'
      }
    },
    currentAmount: {
      type: Number,
      default: 0,
      min: [0, 'Current amount cannot be negative'],
      validate: {
        validator: function (this: IGoal, value: number) {
          return value <= this.targetAmount;
        },
        message: 'Current amount cannot exceed target amount'
      }
    },
    startDate: {
      type: Date,
      default: Date.now,
    },
    targetDate: {
      type: Date,
      required: true,
    },
    priority: {
      type: String,
      enum: ['high', 'medium', 'low'],
      default: 'medium',
    },
    category: {
      type: String,
      required: true,
      trim: true,
    },
    status: {
      type: String,
      enum: ['active', 'completed', 'behind', 'ahead', 'paused'],
      default: 'active',
    },
  },
  {
    timestamps: true,
  }
);

// Add compound indexes for efficient queries
goalSchema.index({ userId: 1, status: 1 });
goalSchema.index({ userId: 1, targetDate: 1 });
goalSchema.index({ userId: 1, priority: 1 });
goalSchema.index({ userId: 1, category: 1 });

// Virtual for progress percentage
goalSchema.virtual('progressPercentage').get(function (this: IGoal) {
  return Math.min((this.currentAmount / this.targetAmount) * 100, 100);
});

// Virtual for remaining amount
goalSchema.virtual('remainingAmount').get(function (this: IGoal) {
  return Math.max(0, this.targetAmount - this.currentAmount);
});

// Pre-save middleware to update status based on progress
goalSchema.pre('save', function (this: IGoal, next) {
  const progress = this.currentAmount / this.targetAmount;

  if (progress >= 1) {
    this.status = 'completed';
  } else if (this.status === 'completed' && progress < 1) {
    this.status = 'active'; // Reset if amount was reduced
  }

  next();
});

// Static method to find goals by user with progress calculation
goalSchema.statics.findByUserWithProgress = function (userId: string, filter: any = {}) {
  return this.aggregate([
    {
      $match: { userId: new mongoose.Types.ObjectId(userId), ...filter }
    },
    {
      $addFields: {
        progressPercentage: {
          $multiply: [
            { $divide: ['$currentAmount', '$targetAmount'] },
            100
          ]
        },
        remainingAmount: {
          $subtract: ['$targetAmount', '$currentAmount']
        },
        daysUntilTarget: {
          $divide: [
            { $subtract: ['$targetDate', new Date()] },
            1000 * 60 * 60 * 24
          ]
        }
      }
    },
    {
      $sort: { targetDate: 1 }
    }
  ]);
};

export const Goal = mongoose.model<IGoal>('Goal', goalSchema);
