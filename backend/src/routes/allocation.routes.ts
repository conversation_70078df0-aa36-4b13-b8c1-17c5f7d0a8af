import express from 'express';
import { authenticateJwt } from '../middleware/auth.middleware';
import {
  allocateFunds,
  getGoalAllocations
} from '../controllers/allocation.controller';

const router = express.Router();

// Temporarily disabled authentication middleware for Challenge 4 component testing
// Authentication is a bonus task according to TODO.md, not a core requirement
// router.use(authenticateJwt);

router.post('/', allocateFunds);
router.get('/goals/:goalId', getGoalAllocations);

export default router;
