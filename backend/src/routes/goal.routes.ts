import express from 'express';
import { authenticateJwt } from '../middleware/auth.middleware';
import {
  getGoals,
  getGoalById,
  createGoal,
  updateGoal,
  deleteGoal
} from '../controllers/goal.controller';

const router = express.Router();

// Temporarily disabled authentication middleware for Challenge 4 component testing
// Authentication is a bonus task according to TODO.md, not a core requirement
// router.use(authenticateJwt);

router.get('/', getGoals);
router.get('/:id', getGoalById);
router.post('/', createGoal);
router.put('/:id', updateGoal);
router.delete('/:id', deleteGoal);

export default router;
