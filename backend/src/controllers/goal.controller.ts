import { Request, Response } from 'express';
import { Goal } from '../models/goal.model';
import { Allocation } from '../models/allocation.model';

// Helper function to calculate comprehensive goal progress and status
const calculateGoalProgress = (goal: any) => {
  const progress = goal.currentAmount / goal.targetAmount;
  const progressPercentage = Math.min(progress * 100, 100);

  const currentDate = new Date();
  const startDate = new Date(goal.startDate);
  const targetDate = new Date(goal.targetDate);

  const daysUntilTarget = Math.ceil((targetDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24));
  const daysFromStart = Math.ceil((currentDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  const totalDays = Math.ceil((targetDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  const expectedProgress = totalDays > 0 ? (daysFromStart / totalDays) * 100 : 0;

  // Calculate monthly savings needed
  const remainingAmount = Math.max(0, goal.targetAmount - goal.currentAmount);
  const monthsRemaining = Math.max(1, daysUntilTarget / 30);
  const monthlySavingsNeeded = remainingAmount / monthsRemaining;

  return {
    progressPercentage: Math.round(progressPercentage * 100) / 100,
    remainingAmount,
    daysUntilTarget,
    expectedProgress: Math.round(expectedProgress * 100) / 100,
    isOnTrack: progressPercentage >= expectedProgress - 10, // 10% tolerance
    isCompleted: progressPercentage >= 100,
    isOverdue: daysUntilTarget < 0,
    monthlySavingsNeeded: Math.round(monthlySavingsNeeded * 100) / 100,
    timeProgress: totalDays > 0 ? Math.round((daysFromStart / totalDays) * 100) : 0
  };
};

// Get all goals with filtering and sorting
export const getGoals = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user._id;
    const filter: any = { userId };

    // Filter by status
    if (req.query.status) {
      filter.status = req.query.status;
    }

    // Determine sort order
    const sortField = req.query.sortBy || 'targetDate';
    const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;
    const sort: any = {};
    sort[sortField as string] = sortOrder;

    const goals = await Goal.find(filter).sort(sort);

    // Calculate comprehensive progress for each goal
    const goalsWithProgress = goals.map(goal => {
      const goalObj = goal.toObject();
      const progressData = calculateGoalProgress(goalObj);
      return { ...goalObj, ...progressData };
    });

    res.json({
      data: goalsWithProgress
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error' });
  }
};

// Get goal by ID
export const getGoalById = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user._id;
    const goal = await Goal.findOne({
      _id: req.params.id,
      userId
    });

    if (!goal) {
      res.status(404).json({ message: 'Goal not found' });
      return;
    }

    // Calculate comprehensive progress
    const goalObj = goal.toObject();
    const progressData = calculateGoalProgress(goalObj);

    res.json({ ...goalObj, ...progressData });
  } catch (error) {
    res.status(500).json({ message: 'Server error' });
  }
};

// Create a new goal
export const createGoal = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user._id;
    const { name, targetAmount, targetDate, priority, category } = req.body;

    const goal = await Goal.create({
      userId,
      name,
      targetAmount,
      currentAmount: 0,
      startDate: new Date(),
      targetDate,
      priority,
      category,
      status: 'active'
    });

    // Calculate progress
    const goalObj = goal.toObject();
    const progress = 0; // New goal starts at 0%

    res.status(201).json({ ...goalObj, progress });
  } catch (error) {
    res.status(500).json({ message: 'Server error' });
  }
};

// Update a goal
export const updateGoal = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user._id;
    const { name, targetAmount, targetDate, priority, category, status } = req.body;

    const goal = await Goal.findOneAndUpdate(
      { _id: req.params.id, userId },
      {
        name,
        targetAmount,
        targetDate,
        priority,
        category,
        status
      },
      { new: true, runValidators: true }
    );

    if (!goal) {
      res.status(404).json({ message: 'Goal not found' });
      return;
    }

    // Calculate progress
    const goalObj = goal.toObject();
    const progress = Math.round((goal.currentAmount / goal.targetAmount) * 100);

    res.json({ ...goalObj, progress });
  } catch (error) {
    res.status(500).json({ message: 'Server error' });
  }
};

// Delete a goal
export const deleteGoal = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user._id;
    const goal = await Goal.findOneAndDelete({
      _id: req.params.id,
      userId
    });

    if (!goal) {
      res.status(404).json({ message: 'Goal not found' });
      return;
    }

    res.status(204).end();
  } catch (error) {
    res.status(500).json({ message: 'Server error' });
  }
};
