import { Request, Response } from 'express';
import mongoose from 'mongoose';
import { Allocation } from '../models/allocation.model';
import { Goal } from '../models/goal.model';
import { AllocationService, PriorityBasedAllocation, TimelineBasedAllocation, BalancedAllocation } from '../services/allocation.service';

// Helper function to update goal statuses based on progress and timeline
const updateGoalStatuses = async (userId: string, session?: mongoose.ClientSession) => {
  const goals = await Goal.find({ userId }).session(session || null);
  const currentDate = new Date();

  for (const goal of goals) {
    const progress = goal.currentAmount / goal.targetAmount;
    const daysUntilTarget = Math.ceil((goal.targetDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24));
    const daysFromStart = Math.ceil((currentDate.getTime() - goal.startDate.getTime()) / (1000 * 60 * 60 * 24));
    const totalDays = Math.ceil((goal.targetDate.getTime() - goal.startDate.getTime()) / (1000 * 60 * 60 * 24));
    const expectedProgress = totalDays > 0 ? daysFromStart / totalDays : 0;

    let newStatus = goal.status;

    if (progress >= 1) {
      newStatus = 'completed';
    } else if (daysUntilTarget <= 0) {
      newStatus = 'behind'; // Past due date
    } else if (progress > expectedProgress + 0.1) {
      newStatus = 'ahead';
    } else if (progress < expectedProgress - 0.1) {
      newStatus = 'behind';
    } else {
      newStatus = 'active';
    }

    if (newStatus !== goal.status) {
      await Goal.findByIdAndUpdate(
        goal._id,
        { status: newStatus },
        { session: session || undefined }
      );
    }
  }
};

// Allocate funds to goals
export const allocateFunds = async (req: Request, res: Response): Promise<void> => {
  const session = await mongoose.startSession();

  try {
    await session.withTransaction(async () => {
      const userId = req.user._id;
      const { amount, strategy, constraints, source } = req.body;

      if (!amount || amount <= 0) {
        res.status(400).json({ message: 'Invalid allocation amount' });
        return;
      }

      // Create appropriate allocation strategy based on request
      let allocationStrategy;
      switch (strategy) {
        case 'priority':
          allocationStrategy = new PriorityBasedAllocation();
          break;
        case 'timeline':
          allocationStrategy = new TimelineBasedAllocation();
          break;
        case 'balanced':
        default:
          allocationStrategy = new BalancedAllocation();
          break;
      }

      // Create allocation service with the selected strategy
      const allocationService = new AllocationService(allocationStrategy);

      // Perform allocation
      const result = await allocationService.allocateFunds(amount, userId, constraints);

      if (result.allocations.length === 0) {
        res.status(400).json({ message: 'No eligible goals found for allocation' });
        return;
      }

      // Create allocation records and update goal amounts
      const allocationRecords = [];

      for (const allocation of result.allocations) {
        // Create allocation record
        const newAllocation = await Allocation.create([{
          userId,
          goalId: allocation.goalId,
          amount: allocation.amount,
          date: new Date(),
          source: source || 'Manual Allocation'
        }], { session });

        // Update goal's current amount
        await Goal.findByIdAndUpdate(
          allocation.goalId,
          { $inc: { currentAmount: allocation.amount } },
          { session }
        );

        allocationRecords.push(newAllocation[0]);
      }

      // Update goal statuses based on new amounts
      await updateGoalStatuses(userId, session);

      res.status(201).json({
        totalAllocated: result.totalAllocated,
        remainingAmount: result.remainingAmount,
        allocations: allocationRecords.map(record => ({
          id: record._id,
          goalId: record.goalId,
          amount: record.amount,
          source: record.source,
          date: record.date,
          createdAt: record.createdAt
        }))
      });
    });
  } catch (error) {
    console.error('Allocation error:', error);
    res.status(500).json({ message: 'Server error during allocation' });
  } finally {
    await session.endSession();
  }
};

// Get allocations for a goal
export const getGoalAllocations = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user._id;
    const goalId = req.params.goalId;

    // Verify goal exists and belongs to user
    const goal = await Goal.findOne({ _id: goalId, userId });
    if (!goal) {
      res.status(404).json({ message: 'Goal not found' });
      return;
    }

    // Get allocations for the goal
    const allocations = await Allocation.find({ goalId, userId })
      .sort({ date: -1 });

    res.json({ data: allocations });
  } catch (error) {
    res.status(500).json({ message: 'Server error' });
  }
};
