import { AllocationService, PriorityBasedAllocation, TimelineBasedAllocation, BalancedAllocation } from '../services/allocation.service';

// Mock goals for testing
const mockGoals = [
  {
    _id: 'goal1',
    name: 'Emergency Fund',
    targetAmount: 10000,
    currentAmount: 2000,
    priority: 'high',
    targetDate: new Date('2025-12-31'),
    status: 'active'
  },
  {
    _id: 'goal2',
    name: 'Vacation',
    targetAmount: 5000,
    currentAmount: 1000,
    priority: 'medium',
    targetDate: new Date('2025-06-30'),
    status: 'active'
  },
  {
    _id: 'goal3',
    name: 'New Laptop',
    targetAmount: 2000,
    currentAmount: 500,
    priority: 'low',
    targetDate: new Date('2025-09-30'),
    status: 'active'
  }
];

// Test constraints
const constraints = {
  minimumAllocation: 100,
  maximumPerGoal: 5000
};

describe('Allocation Service', () => {
  describe('PriorityBasedAllocation', () => {
    let service: AllocationService;

    beforeEach(() => {
      service = new AllocationService(new PriorityBasedAllocation());
    });

    test('should allocate funds based on priority', () => {
      const strategy = new PriorityBasedAllocation();
      const result = strategy.allocate(1000, mockGoals);

      // High priority goal should get allocation first
      const emergencyFundAllocation = result.find(r => r.goalId === 'goal1');
      const vacationAllocation = result.find(r => r.goalId === 'goal2');
      const laptopAllocation = result.find(r => r.goalId === 'goal3');

      expect(emergencyFundAllocation).toBeDefined();
      expect(emergencyFundAllocation!.amount).toBeGreaterThan(0);

      // Since we have $1000 and emergency fund needs $8000, it should get all the money
      expect(emergencyFundAllocation!.amount).toBe(1000);
      expect(vacationAllocation?.amount || 0).toBe(0);
      expect(laptopAllocation?.amount || 0).toBe(0);
    });

    test('should respect minimum allocation constraint', () => {
      const strategy = new PriorityBasedAllocation();
      const result = strategy.allocate(250, mockGoals, { minimumAllocation: 100 });

      // With minimum allocation of 100, we can only fund 2 goals with 250
      const allocatedGoals = result.filter(r => r.amount > 0);
      expect(allocatedGoals.length).toBeLessThanOrEqual(2);

      // Each allocated goal should have at least the minimum
      allocatedGoals.forEach(allocation => {
        expect(allocation.amount).toBeGreaterThanOrEqual(100);
      });
    });

    test('should respect maximum per goal constraint', () => {
      const strategy = new PriorityBasedAllocation();
      const result = strategy.allocate(10000, mockGoals, { maximumPerGoal: 500 });

      // No goal should receive more than the maximum
      result.forEach(allocation => {
        expect(allocation.amount).toBeLessThanOrEqual(500);
      });
    });

    test('should handle edge case of insufficient funds', () => {
      const strategy = new PriorityBasedAllocation();
      const result = strategy.allocate(50, mockGoals, { minimumAllocation: 100 });

      // With insufficient funds, no allocations should be made
      expect(result.length).toBe(0);
    });
  });

  describe('TimelineBasedAllocation', () => {
    let service: AllocationService;

    beforeEach(() => {
      service = new AllocationService(new TimelineBasedAllocation());
    });

    test('should allocate funds based on target date proximity', () => {
      const strategy = new TimelineBasedAllocation();
      const result = strategy.allocate(1000, mockGoals);

      // Vacation has closest deadline (June 30), should get most allocation
      const vacationAllocation = result.find(r => r.goalId === 'goal2');
      const emergencyFundAllocation = result.find(r => r.goalId === 'goal1');
      const laptopAllocation = result.find(r => r.goalId === 'goal3');

      expect(vacationAllocation).toBeDefined();
      expect(vacationAllocation!.amount).toBeGreaterThan(0);

      // Vacation should get more than the others due to closer deadline
      if (emergencyFundAllocation) {
        expect(vacationAllocation!.amount).toBeGreaterThan(emergencyFundAllocation.amount);
      }
      if (laptopAllocation) {
        expect(vacationAllocation!.amount).toBeGreaterThan(laptopAllocation.amount);
      }
    });

    test('should prioritize goals with closer deadlines', () => {
      const strategy = new TimelineBasedAllocation();
      const result = strategy.allocate(500, mockGoals);

      // Should allocate to all goals but with preference for closer deadlines
      const totalAllocated = result.reduce((sum, alloc) => sum + alloc.amount, 0);
      expect(totalAllocated).toBeGreaterThan(0);
      expect(totalAllocated).toBeLessThanOrEqual(500);

      // Vacation (closest deadline) should get some allocation
      const vacationAllocation = result.find(r => r.goalId === 'goal2');
      expect(vacationAllocation?.amount || 0).toBeGreaterThan(0);
    });
  });

  describe('BalancedAllocation', () => {
    let service: AllocationService;

    beforeEach(() => {
      service = new AllocationService(new BalancedAllocation());
    });

    test('should distribute funds across all goals', () => {
      const strategy = new BalancedAllocation();
      const result = strategy.allocate(1000, mockGoals);

      // All goals should receive some allocation in balanced approach
      expect(result.length).toBeGreaterThan(0);

      // Each goal should get some allocation
      const emergencyFundAllocation = result.find(r => r.goalId === 'goal1');
      const vacationAllocation = result.find(r => r.goalId === 'goal2');
      const laptopAllocation = result.find(r => r.goalId === 'goal3');

      expect(emergencyFundAllocation?.amount || 0).toBeGreaterThan(0);
      expect(vacationAllocation?.amount || 0).toBeGreaterThan(0);
      expect(laptopAllocation?.amount || 0).toBeGreaterThan(0);

      // Total should not exceed the input amount
      const totalAllocated = result.reduce((sum, alloc) => sum + alloc.amount, 0);
      expect(totalAllocated).toBeLessThanOrEqual(1000);
    });

    test('should balance priority and timeline considerations', () => {
      const strategy = new BalancedAllocation();
      const result = strategy.allocate(600, mockGoals);

      // High priority goal (Emergency Fund) should still get more than low priority
      const emergencyFundAllocation = result.find(r => r.goalId === 'goal1');
      const laptopAllocation = result.find(r => r.goalId === 'goal3');

      if (emergencyFundAllocation && laptopAllocation) {
        expect(emergencyFundAllocation.amount).toBeGreaterThan(laptopAllocation.amount);
      }

      // But vacation (closer deadline) should also get significant allocation
      const vacationAllocation = result.find(r => r.goalId === 'goal2');
      expect(vacationAllocation?.amount || 0).toBeGreaterThan(0);

      // Percentages should sum to approximately 100%
      const totalPercentage = result.reduce((sum, alloc) => sum + alloc.percentage, 0);
      expect(totalPercentage).toBeCloseTo(100, 1);
    });
  });
});
