import { Document, Types } from 'mongoose';

// Base interfaces for database documents
export interface I<PERSON>ser extends Document {
  _id: Types.ObjectId;
  email: string;
  password: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IGoal extends Document {
  _id: Types.ObjectId;
  userId: Types.ObjectId;
  name: string;
  targetAmount: number;
  currentAmount: number;
  startDate: Date;
  targetDate: Date;
  priority: 'low' | 'medium' | 'high';
  status: 'active' | 'completed' | 'paused' | 'behind' | 'ahead';
  category?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ITransaction extends Document {
  _id: Types.ObjectId;
  userId: Types.ObjectId;
  amount: number;
  type: 'income' | 'expense' | 'transfer';
  category: string;
  merchant?: string;
  description?: string;
  date: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface IAllocation extends Document {
  _id: Types.ObjectId;
  userId: Types.ObjectId;
  goalId: Types.ObjectId;
  amount: number;
  source: string;
  date: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Enhanced goal interface with computed properties
export interface IGoalWithProgress extends IGoal {
  progressPercentage: number;
  remainingAmount: number;
  daysUntilTarget: number;
  expectedProgress: number;
  isOnTrack: boolean;
  isCompleted: boolean;
  isOverdue: boolean;
  monthlySavingsNeeded: number;
  timeProgress: number;
}

// Transaction summary interfaces
export interface ITransactionSummary {
  totalIncome: number;
  totalExpenses: number;
  netAmount: number;
  transactionCount: number;
  averageTransaction: number;
  categoryBreakdown: ICategoryBreakdown[];
}

export interface ICategoryBreakdown {
  category: string;
  amount: number;
  count: number;
  percentage: number;
}

// Allocation interfaces
export interface AllocationResult {
  goalId: string;
  amount: number;
  percentage: number;
}

export interface AllocationOptions {
  minimumAllocation?: number;
  maximumPerGoal?: number;
  excludeGoals?: string[];
}

export interface AllocationStrategy {
  allocate(amount: number, goals: any[], options?: AllocationOptions): AllocationResult[];
}

export interface AllocationServiceResult {
  totalAllocated: number;
  remainingAmount: number;
  allocations: AllocationResult[];
  strategy: string;
}

// API Request/Response interfaces
export interface CreateGoalRequest {
  name: string;
  targetAmount: number;
  targetDate: string | Date;
  priority: 'low' | 'medium' | 'high';
  category?: string;
}

export interface UpdateGoalRequest {
  name?: string;
  targetAmount?: number;
  targetDate?: string | Date;
  priority?: 'low' | 'medium' | 'high';
  status?: 'active' | 'completed' | 'paused' | 'behind' | 'ahead';
  category?: string;
}

export interface CreateTransactionRequest {
  amount: number;
  type: 'income' | 'expense' | 'transfer';
  category: string;
  merchant?: string;
  description?: string;
  date?: string | Date;
}

export interface AllocateFundsRequest {
  amount: number;
  strategy: 'priority' | 'timeline' | 'balanced';
  constraints?: AllocationOptions;
  source?: string;
}

// Query interfaces
export interface GoalQueryParams {
  status?: 'active' | 'completed' | 'paused' | 'behind' | 'ahead';
  priority?: 'low' | 'medium' | 'high';
  sortBy?: 'name' | 'targetAmount' | 'targetDate' | 'priority' | 'createdAt' | 'progress';
  sortOrder?: 'asc' | 'desc';
}

export interface TransactionQueryParams {
  type?: 'income' | 'expense' | 'transfer';
  category?: string;
  startDate?: string | Date;
  endDate?: string | Date;
  minAmount?: number;
  maxAmount?: number;
  sortBy?: 'date' | 'amount' | 'category' | 'merchant';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

// Error interfaces
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

export interface ApiError {
  message: string;
  errors?: ValidationError[];
  statusCode?: number;
}

// Dashboard data interfaces
export interface DashboardData {
  goals: IGoalWithProgress[];
  recentTransactions: ITransaction[];
  transactionSummary: ITransactionSummary;
  goalsSummary: {
    total: number;
    completed: number;
    active: number;
    behind: number;
    ahead: number;
  };
  monthlyProgress: {
    month: string;
    totalSaved: number;
    totalAllocated: number;
    goalsCompleted: number;
  }[];
}

// Utility types
export type GoalStatus = 'active' | 'completed' | 'paused' | 'behind' | 'ahead';
export type GoalPriority = 'low' | 'medium' | 'high';
export type TransactionType = 'income' | 'expense' | 'transfer';
export type AllocationStrategyType = 'priority' | 'timeline' | 'balanced';

// Express Request extensions
declare global {
  namespace Express {
    interface Request {
      user?: IUser;
      goal?: IGoal;
    }
  }
}

// Database connection interface
export interface DatabaseConfig {
  uri: string;
  options?: {
    useNewUrlParser?: boolean;
    useUnifiedTopology?: boolean;
    maxPoolSize?: number;
    serverSelectionTimeoutMS?: number;
    socketTimeoutMS?: number;
    family?: number;
  };
}

// Environment configuration
export interface AppConfig {
  port: number;
  mongoUri: string;
  jwtSecret: string;
  jwtExpiresIn: string;
  nodeEnv: 'development' | 'production' | 'test';
  corsOrigin: string;
}

// Pagination interface
export interface PaginationResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Service response interface
export interface ServiceResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: ValidationError[];
}

export default {
  IUser,
  IGoal,
  ITransaction,
  IAllocation,
  IGoalWithProgress,
  AllocationResult,
  AllocationOptions,
  AllocationStrategy,
  CreateGoalRequest,
  UpdateGoalRequest,
  CreateTransactionRequest,
  AllocateFundsRequest,
  GoalQueryParams,
  TransactionQueryParams,
  ValidationError,
  ApiError,
  DashboardData
};
