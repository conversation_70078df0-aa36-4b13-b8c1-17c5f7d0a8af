import { PriorityBasedAllocation, TimelineBasedAllocation, BalancedAllocation } from './services/allocation.service';

// Demo goals based on the documentation examples
const demoGoals = [
  {
    _id: 'goal1',
    name: 'Emergency Fund',
    targetAmount: 10000,
    currentAmount: 2000,
    priority: 'high' as const,
    targetDate: new Date('2025-12-31'),
    status: 'active' as const
  },
  {
    _id: 'goal2',
    name: 'Vacation',
    targetAmount: 5000,
    currentAmount: 1000,
    priority: 'medium' as const,
    targetDate: new Date('2025-06-30'),
    status: 'active' as const
  },
  {
    _id: 'goal3',
    name: 'New Laptop',
    targetAmount: 2000,
    currentAmount: 500,
    priority: 'low' as const,
    targetDate: new Date('2025-09-30'),
    status: 'active' as const
  }
];

function formatCurrency(amount: number): string {
  return `$${amount.toLocaleString()}`;
}

function printAllocationResults(strategy: string, results: any[], amount: number) {
  console.log(`\n=== ${strategy} Strategy ===`);
  console.log(`Total Amount to Allocate: ${formatCurrency(amount)}`);
  console.log('Results:');
  
  if (results.length === 0) {
    console.log('  No allocations made');
    return;
  }
  
  results.forEach(result => {
    const goal = demoGoals.find(g => g._id === result.goalId);
    console.log(`  ${goal?.name}: ${formatCurrency(result.amount)} (${result.percentage.toFixed(1)}%)`);
  });
  
  const totalAllocated = results.reduce((sum, r) => sum + r.amount, 0);
  console.log(`Total Allocated: ${formatCurrency(totalAllocated)}`);
  console.log(`Remaining: ${formatCurrency(amount - totalAllocated)}`);
}

function runDemo() {
  console.log('🎯 FinTrack Allocation Algorithm Demo');
  console.log('=====================================');
  
  // Display goals
  console.log('\nGoals:');
  demoGoals.forEach(goal => {
    const progress = ((goal.currentAmount / goal.targetAmount) * 100).toFixed(1);
    const remaining = goal.targetAmount - goal.currentAmount;
    console.log(`  ${goal.name}: ${formatCurrency(goal.currentAmount)}/${formatCurrency(goal.targetAmount)} (${progress}%) - ${goal.priority} priority, due ${goal.targetDate.toDateString()}, needs ${formatCurrency(remaining)}`);
  });
  
  const amount = 1000;
  const constraints = { minimumAllocation: 100 };
  
  // Test Priority-Based Allocation
  const priorityStrategy = new PriorityBasedAllocation();
  const priorityResults = priorityStrategy.allocate(amount, demoGoals, constraints);
  printAllocationResults('Priority-Based', priorityResults, amount);
  
  // Test Timeline-Based Allocation
  const timelineStrategy = new TimelineBasedAllocation();
  const timelineResults = timelineStrategy.allocate(amount, demoGoals, constraints);
  printAllocationResults('Timeline-Based', timelineResults, amount);
  
  // Test Balanced Allocation
  const balancedStrategy = new BalancedAllocation();
  const balancedResults = balancedStrategy.allocate(amount, demoGoals, constraints);
  printAllocationResults('Balanced', balancedResults, amount);
  
  console.log('\n✅ Demo completed successfully!');
  console.log('\nKey Features Demonstrated:');
  console.log('- Strategy Pattern implementation');
  console.log('- Priority-based allocation (high priority gets funds first)');
  console.log('- Timeline-based allocation (closer deadlines get more funds)');
  console.log('- Balanced allocation (all goals get some allocation)');
  console.log('- Constraint handling (minimum allocation requirements)');
  console.log('- Edge case handling (insufficient funds, completed goals)');
}

// Run the demo
runDemo();
